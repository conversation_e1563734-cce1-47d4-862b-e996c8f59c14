#!/usr/bin/env python3
"""
Test script to simulate S3 event parsing logic.
This helps validate S3 event structure without requiring Kubernetes dependencies.
"""

import json

# Sample S3 event structure
test_event = {
    "Records": [
        {
            "eventVersion": "2.1",
            "eventSource": "aws:s3",
            "awsRegion": "ca-central-1",
            "eventTime": "2025-01-08T10:30:00.000Z",
            "eventName": "ObjectCreated:Put",
            "s3": {
                "s3SchemaVersion": "1.0",
                "configurationId": "etransfer-recon-trigger",
                "bucket": {
                    "name": "pg-gl-recon-qa",
                    "arn": "arn:aws:s3:::pg-gl-recon-qa"
                },
                "object": {
                    "key": "input/etransfer_gl_recon_799455639446_20250108.xml",
                    "size": 1024,
                    "eTag": "d41d8cd98f00b204e9800998ecf8427e"
                }
            }
        }
    ]
}

# Mock context object
class MockContext:
    def __init__(self):
        self.aws_request_id = "test-request-id-12345"
        self.function_name = "test-etransfer-lambda"
        self.function_version = "$LATEST"

def extract_s3_info(event):
    """Extract S3 bucket and key from event - same logic as in Lambda."""
    s3_bucket = None
    s3_key = None
    
    print(f"Received event: {json.dumps(event, indent=2)}")
    
    # Parse S3 event to extract bucket and key
    if 'Records' in event and len(event['Records']) > 0:
        s3_record = event['Records'][0]
        if 's3' in s3_record:
            s3_bucket = s3_record['s3']['bucket']['name']
            s3_key = s3_record['s3']['object']['key']
            print(f"✅ S3 Event - Bucket: {s3_bucket}, Key: {s3_key}")
        else:
            print("❌ No S3 information found in event record")
            raise Exception("Invalid S3 event format - missing s3 data")
    else:
        print("❌ No Records found in event")
        raise Exception("Invalid event format - missing Records")
    
    if not s3_bucket or not s3_key:
        raise Exception(f"Failed to extract S3 information - Bucket: {s3_bucket}, Key: {s3_key}")
    
    return s3_bucket, s3_key

def test_argument_override(original_args, s3_bucket, s3_key):
    """Test the argument override logic - same as in Lambda."""
    print(f"\nTesting argument override...")
    print(f"Original args: {original_args}")
    
    updated_args = []
    i = 0
    while i < len(original_args):
        arg = original_args[i]
        if arg == "--s3-bucket" and i + 1 < len(original_args):
            updated_args.append(arg)
            updated_args.append(s3_bucket)  # Use dynamic bucket from S3 event
            print(f"✅ Overriding --s3-bucket with: {s3_bucket}")
            i += 2  # Skip the next argument as it's the old bucket value
        elif arg == "--s3-key" and i + 1 < len(original_args):
            updated_args.append(arg)
            updated_args.append(s3_key)  # Use dynamic key from S3 event
            print(f"✅ Overriding --s3-key with: {s3_key}")
            i += 2  # Skip the next argument as it's the old key value
        else:
            updated_args.append(arg)
            i += 1
    
    print(f"Updated args: {updated_args}")
    return updated_args

def test_s3_event_processing():
    """Test S3 event processing without Kubernetes dependencies."""
    print("=== Testing S3 Event Processing Logic ===\n")
    
    try:
        # Test S3 info extraction
        s3_bucket, s3_key = extract_s3_info(test_event)
        
        # Test argument override with typical CronJob args
        original_args = [
            "--s3-bucket",
            "PLACEHOLDER_BUCKET",
            "--s3-key", 
            "PLACEHOLDER_KEY"
        ]
        
        updated_args = test_argument_override(original_args, s3_bucket, s3_key)
        
        print(f"\n✅ Test completed successfully!")
        print(f"   Extracted bucket: {s3_bucket}")
        print(f"   Extracted key: {s3_key}")
        print(f"   Final args: {updated_args}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_s3_event_processing()