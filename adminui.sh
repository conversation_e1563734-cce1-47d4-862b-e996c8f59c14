#!/usr/bin/env bash

# This script updates and deploys a frontend application.
# It accepts two arguments: [environment] [aws_profile]
# Environment: "qa" or "staging" (case-insensitive)
# AWS profile: Your AWS profile name (default: NON_LEDGER)

# Normalize environment to lowercase
ENVIRONMENT=$(echo "$1" | tr '[:upper:]' '[:lower:]')

# Use provided AWS profile or default to NON_LEDGER if not provided
AWS_PROFILE="${2:-NON_LEDGER}"

# Determine configuration based on environment
case "$ENVIRONMENT" in
  "qa")
    DISTRO_ID="E16KRLV292Y0P0"
    BUCKET="gl-qa-799455639446"
    ;;
  "staging")
    DISTRO_ID="ETN21A52AJ36F"
    BUCKET="gl-staging-799455639446"
    ;;
  *)
    echo "Error: Invalid environment specified. Please use 'qa' or 'staging'."
    exit 1
    ;;
esac

echo "Starting deployment to '$ENVIRONMENT' environment..."
echo "Using AWS Profile: $AWS_PROFILE"

# Move into the frontend directory
cd frontend || { echo "Error: 'frontend' directory not found."; exit 1; }

echo "Installing dependencies..."
if ! npm install --force; then
  echo "Error: Failed to install dependencies."
  exit 1
fi

echo "Building project for $ENVIRONMENT environment..."
if ! npm run build:"$ENVIRONMENT"; then
  echo "Error: Build failed."
  exit 1
fi

echo "Syncing build output to S3 bucket: $BUCKET"
if ! aws s3 sync build/ "s3://$BUCKET" --profile "$AWS_PROFILE"; then
  echo "Error: Failed to sync files to S3."
  exit 1
fi

echo "Creating CloudFront cache invalidation on distribution: $DISTRO_ID"
if ! aws cloudfront create-invalidation --distribution-id "$DISTRO_ID" --paths "/*" --profile "$AWS_PROFILE"; then
  echo "Error: Failed to create CloudFront invalidation."
  exit 1
fi

echo "Deployment to $ENVIRONMENT environment complete."
