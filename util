#!/bin/bash

# DEBUG
#set -o xtrace

# usage function
function usage() {
    cat << USAGEDOC

Usage: ${0} [-p PROJECT] -l LIFECYCLE [-t TAG] [-s] [-v]

    PROJECT to build, one of:
        ledger-profile
        ledger-account
        ledger-transaction
        ledger-schedulers
        ledger-account-external
        ledger-rollback-scheduler
        qa-util
        ledger-transaction-async
        ledger-transaction-async-listener
        ledger-health

    LIFECYCLE to execute, one of:
        <PERSON><PERSON><PERSON>               builds the deployable artifact (Spring Boot app)
        CONTAINER           bundle into container image (includes BUILD lifecycle)
        PUBLISH             publish docker image to ECR (includes CONTAINER and B<PERSON><PERSON> lifecycle)

        LOCAL_DB            deploy local PostgresSQL DB + PgAdmin
        LOCAL_DB_REMOVE     remove local PostgresSQL DB + PgAdmin

        CLEAN_LOCAL_REPO    clears local Docker repository (and remote tags)

    Optional arguments:
        -s          skip jUnit test execution
        -v          full verbose logging
        -c          build in the cloud with CodeBuild
        -t TAG      container tag to deploy (required for DEPLOY lifecycle, otherwise ignored)
USAGEDOC
}

#################
# CONFIGURATION #
#################

# OUTPUT (LOG) FILE
LOG_FILE="${PWD}/log-util."$(date +%Y%m%d%H%M%S)".log"

#
# TBC -- update these to POINT TO GL DB schemas
PG_CUSTOMER_DDL="customer-persistence/src/datamodel/2.0.1/create.sql"
PG_CUSTOMER_SEED="customer-persistence/src/datamodel/2.0.1/seed.sql"

PG_PAYMENT_DDL="payment-persistence/src/datamodel/2.0.1/create.sql"
PG_PAYMENT_SEED="payment-persistence/src/datamodel/2.0.1/seed.sql"

PG_SYSTEM_DDL="service-account-persistence/src/datamodel/*******/create.sql"
PG_SYSTEM_SEED="service-account-persistence/src/datamodel/*******/seed.sql"

# global variables, to hold what the DDLs should be.
PG_DB_DDL=
PG_DB_SEED=

# Configuration values
DOCKER_REPO_BASE="************.dkr.ecr.ca-central-1.amazonaws.com"

# Paths on the local filesystem, referred to by the docker container
APP_FOLDER="/opt/peoplestrust"
PROJECT_DATA_FOLDER="${APP_FOLDER}/banking-data"

SETUP_MARKER="${APP_FOLDER}/.pg_banking"

#
# Generated/target files
PG_DDL="target/PG_DDL.sql"
PG_SEED="target/PG_SEED.sql"

# load util functions
source environments/scripts/util-common.sh

# define all available projects
ALL_PROJECTS=("ledger-profile" "ledger-account" "ledger-transaction" "ledger-schedulers" "ledger-account-external" "qa-util" "ledger-rollback-scheduler" "ledger-transaction-async" "ledger-transaction-async-listener" "ledger-health")
GEN_PROJECT="all"

# Parse ARGUMENTS
while getopts "p:l:t:vsch" options; do
    case "${options}" in
        p)
            PROJECT=${OPTARG}
            ;;
        l)
            LIFECYCLE=${OPTARG}
            ;;
        v)
            VERBOSE=1
            ;;
        s)
            SKIP_TEST=1
            ;;
        c)
            CLOUD=1
            ;;
        t)
            DEPLOY_TAG=${OPTARG}
            ;;
        h)
            usage
            exit
            ;;
        \? )
            exit_abnormal "Unknown option: -$OPTARG" >&2
            ;;
        :  )
            exit_abnormal "Missing option argument for -$OPTARG" >&2
            ;;
        *  )
            exit_abnormal "Unimplemented option: -$OPTARG" >&2
            ;;
    esac
done

# upfront checks/configuration
if [ $# -eq 0 ]; then
    usage
    exit 0
fi
if [[ -z ${LIFECYCLE} ]] 
then
    exit_abnormal "LIFECYCLE not provided"
fi

# Overrides applicable to a build
if [[ -n ${SKIP_TEST} ]]
then
    BUILD_ARGS="-Dmaven.test.skip=true"
    print_debug "INFO Skipping all jUnit test cases"
fi


# ECR authentication in the cloud or locally
if [[ -n ${CLOUD} ]]
then
    PROFILE=""
    echo "INFO Build in the Cloud"
else
    PROFILE="--profile NON_LEDGER"
    echo "INFO Manual Build"
fi
# Indicate where to see log details
echo "Logging all output to ${LOG_FILE}"


if [[ ${LIFECYCLE} == "SETUP" ]]
then
    # in SETUP lifecycle, initialize filesystem
    echo -e "\nPreflight checks"
    
    if [[ $USER != "root" ]]
    then
        print_red "  Check script is executed as root" "FAIL"
        exit 1
    else
        print_green "  Check script is executed as root" "OK"
    fi

    echo -e "\nBuild filesystem for BaaS"
    export PROJECT_DATA_FOLDER=${PROJECT_DATA_FOLDER}
    create_directory_structure

    touch ${SETUP_MARKER}
    print_end "Completed"
else
    # ACTION based on what the lifecycle is
    case ${LIFECYCLE} in
        BUILD)
            # no explicit project defined, build all
            if [[ -z ${PROJECT} ]]
            then
                echo -e "\nCommencing BUILD lifecycle (ALL projects)"
                # build all
                maven_build_all ${BUILD_ARGS}
            else
                echo -e "\nCommencing BUILD lifecycle (${PROJECT} project)"
                # build project
                maven_build_service ${PROJECT} ${BUILD_ARGS}
            fi
            ;;
        CONTAINER)
            # tag for the container image
            DOCKER_IMAGE_TAG=$(date +%Y%m%d%H%M%S)

            # no explicit project defined, build all
            if [[ -z ${PROJECT} ]]
            then
                echo -e "\nCommencing BUILD + CONTAINER lifecycle (ALL projects)"
                # build all
                maven_build_all ${BUILD_ARGS}
                
                # build container for all projects
                for i_container in ${ALL_PROJECTS[@]}
                do
                    # build docker container image
                    docker_build_service ${i_container} ${DOCKER_IMAGE_TAG}
                done
            else
                echo -e "\nCommencing BUILD + CONTAINER lifecycle (${PROJECT} project)"
                # build project
                maven_build_service ${PROJECT} ${BUILD_ARGS}

                # build docker container image
                docker_build_service ${PROJECT} ${DOCKER_IMAGE_TAG}
            fi
            ;;
        PUBLISH)
            # publish (do not build, create image) if tag is supplied
            if [[ -n ${DEPLOY_TAG} ]]
            then
                # use docker image tag provided by the user
                DOCKER_IMAGE_TAG=${DEPLOY_TAG}

                # no explicit project defined, publish all
                if [[ -z ${PROJECT} ]]
                then
                    echo -e "\nCommencing PUBLISH lifecycle (ALL projects)"
                    # build container for all projects
                    for i_publish in ${ALL_PROJECTS[@]}
                    do
                        # publish docker images to ECR
                        docker_publish_ecr ${i_publish} ${DOCKER_IMAGE_TAG} "${PROFILE}"
                    done
                else
                    echo -e "\nCommencing PUBLISH lifecycle (${PROJECT} project)"
                    # publish docker images to ECR
                    docker_publish_ecr ${PROJECT} ${DOCKER_IMAGE_TAG} "${PROFILE}"
                fi
            else
                # generate docker image tag
                DOCKER_IMAGE_TAG=$(date +%Y%m%d%H%M%S)

                # no explicit project defined, build all
                if [[ -z ${PROJECT} ]]
                then
                    echo -e "\nCommencing BUILD + CONTAINER + PUBLISH lifecycle (ALL projects)"
                    # build all
                    maven_build_all ${BUILD_ARGS}
                    
                    # build container for all projects
                    for i_publish in ${ALL_PROJECTS[@]}
                    do
                        # build docker container image
                        docker_build_service ${i_publish} ${DOCKER_IMAGE_TAG}

                        # publish docker images to ECR
                        docker_publish_ecr ${i_publish} ${DOCKER_IMAGE_TAG} "${PROFILE}"
                    done
                else
                    echo -e "\nCommencing BUILD + CONTAINER + PUBLISH lifecycle (${PROJECT} project)"
                    # build project
                    maven_build_service ${PROJECT} ${BUILD_ARGS}

                    # build docker container image
                    docker_build_service ${PROJECT} ${DOCKER_IMAGE_TAG}

                    # publish docker images to ECR
                    docker_publish_ecr ${PROJECT} ${DOCKER_IMAGE_TAG} "${PROFILE}"
                fi
            fi
            ;;
        LOCAL_DB)
            # preflight filesystem directory + files check
            preflight_check

            export PROJECT_DATA_FOLDER=${PROJECT_DATA_FOLDER}
            docker_compose_database ${GEN_PROJECT}
            ;;
        LOCAL_DB_REMOVE)
            docker_compose_database_remove ${GEN_PROJECT}
            ;;
        CLEAN_LOCAL_REPO)
            # clean (if any) orphaned Docker images
            DOCKER_IMAGES=$(docker images | grep '<none>' | tr -s ' ' | cut -d ' ' -f 3)
            if [[ ${DOCKER_IMAGES} != "" ]]
            then
                cmd_with_progress "docker rmi --force \"$(docker images | grep '<none>' | tr -s ' ' | cut -d ' ' -f 3)\"" "   Removing orphaned Docker images"
            else
                print_green "   Removing orphaned Docker images" "OK"
            fi

            # no explicit project defined, remove all
            if [[ -z ${PROJECT} ]]
            then
                # remove images for  all projects
                for i in ${ALL_PROJECTS[@]}
                do
                    # first, determine if there is anything to remove
                    DOCKER_IMAGES=$(docker images | grep '${i}' | tr -s ' ' | cut -d ' ' -f 3)
                    if [[ ${DOCKER_IMAGES} != "" ]]
                    then
                        for IMAGE in ${DOCKER_IMAGES}
                        do
                            # found images to remove
                            cmd_with_progress "docker rmi --force \"${IMAGE}\"" "   Removing ${i}:${IMAGE} Docker images"
                        done
                    else
                        print_green "   Nothing to remove for ${i}" "OK"
                    fi
                done
            else
                # first, determine if there is anything to remove
                DOCKER_IMAGES=$(docker images | grep '${PROJECT}' | tr -s ' ' | cut -d ' ' -f 3)
                if [[ ${DOCKER_IMAGES} != "" ]]
                then
                    # found images to remove
                    cmd_with_progress "docker rmi --force \"${DOCKER_IMAGES}\"" "   Removing all ${PROJECT} Docker images"
                else
                    print_green "   Nothing to remove for ${i}" "OK"
                fi
            fi
            ;;
        * )
            echo "ERROR ${LIFECYCLE} is not a valid selection"
            usage
            exit 1
            ;;
    esac
fi

exit 0;
