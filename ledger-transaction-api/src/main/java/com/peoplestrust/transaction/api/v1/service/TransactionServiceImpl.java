package com.peoplestrust.transaction.api.v1.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.transaction.api.v1.config.TransactionProperty;
import com.peoplestrust.transaction.api.v1.exception.DuplicateInstructionException;
import com.peoplestrust.transaction.api.v1.exception.DuplicateTransactionException;
import com.peoplestrust.transaction.api.v1.mapper.TransactionMapper;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Balance;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.domain.model.*;
import com.peoplestrust.transaction.persistence.entity.*;
import com.peoplestrust.transaction.persistence.entity.InstructionStatus;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionMetadataRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.exception.*;
import com.peoplestrust.util.api.common.util.DateUtils;
import com.peoplestrust.util.api.common.util.Messages;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.ResourceAccessException;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.peoplestrust.transaction.api.v1.util.RailCategoryUtil.determineTransactionFlow;

/**
 * Transaction service implementation.
 */
@Slf4j
@Service
public class TransactionServiceImpl implements TransactionService {

  /**
   * Instruction Persistence Repository
   */
  @Autowired
  InstructionRepository instructionRepository;

  /**
   * Instruction Persistence Repository
   */
  @Autowired
  ReadInstructionRepository readInstructionRepository;

  /**
   * Transaction Persistence Repository
   */
  @Autowired
  ReadTransactionRepository readTransactionRepository;

  /**
   * Balance Persistence Repository
   */
  @Autowired
  ReadBalanceRepository readBalanceRepository;

  /**
   * Transaction Persistence Repository
   */
  @Autowired
  TransactionRepository transactionRepository;

  /**
   * Balance Persistence Repository
   */
  @Autowired
  BalanceRepository balanceRepository;

  /**
   * Balance Persistence Repository
   */
  @Autowired
  TransactionMetadataRepository transactionMetadataRepository;
  /**
   * Transaction mapper.
   */
  @Autowired
  TransactionMapper transactionMapper;

  /**
   * Validation service.
   */
  @Autowired
  ValidationService validationService;

  /**
   * Transaction property configuration
   */
  @Autowired
  TransactionProperty transactionProperty;

  /**
   * Method to create instruction with transactions
   *
   * @param instruction
   * @param profileRefId
   * @param accountRefId
   * @param interactionId
   * @return
   * @throws Exception
   */
  @PerfLogger
  private InstructionEntity createInstruction(Account account, Instruction instruction,
      String profileRefId, String accountRefId, String interactionId) throws Exception {

    /*
     * Part B -- Add instruction & transactions in INIT_PENDING status, ensures these transactions are reflected in case of concurrent postings.
     */
    InstructionStatus insStatus = InstructionStatus.INIT_PENDING;
    TransactionStatus transStatus = TransactionStatus.INIT_PENDING;

    // Update instruction with relevant details (augments mapped data in DTO)
    instruction.setProfileRefId(profileRefId);
    instruction.setAccountRefId(accountRefId);
    instruction.setStatus(insStatus); // update instruction status to INIT_PENDING

    List<Transaction> transactions = instruction.getTransactions(); // maintain reference to transactions
    instruction.setTransactions(null); // and remove from the instruction DTO to prevent them from being inserted into DB when inserting instruction

    // transform DTO to persistence,and persist instruction
    InstructionEntity instructionEntity = transactionMapper.fromInstructionToInstructionEntity(instruction);
    List<TransactionEntity> initiateTransactions;
    try {
      instructionEntity = instructionRepository.save(instructionEntity);
      // persist transactions -- bulk insert into database with INIT_PENDING status
      initiateTransactions = insertInitiateTransactions(account, transactions, accountRefId, profileRefId, interactionId, instructionEntity);

      log.debug("inserted {} instruction with status = {}", instructionEntity, instructionEntity.getStatus());

      /*
       * Part C -- Check account balance (if required)
       * <em>Applicable if there are one or more DEBITS; if only credits, a balance check is not required</em>
       */

      // determine if the instruction contains any DEBIT transactions; balance check only required if any debits exist.
      boolean isBalanceCheckRequired = false; // default is no balance check
      for (Transaction item : transactions) {
        if (item.getTransactionFlow().equals(TransactionFlowType.DEBIT)) {
          isBalanceCheckRequired = true; // if any debit transaction is found, then return true

          log.debug("located at least one DEBIT transaction; transaction_ref_id = {}", item.getTransactionRefId());
          break; // can assume balance check is required if any single DEBIT transaction exists
        }
      }

      // perform balance check (if required)
      boolean isBalanceAvailable;

      if (isBalanceCheckRequired) {
        log.debug("balance check required as there are one or more DEBIT transactions");
        isBalanceAvailable = calculateAccountBalance(account, accountRefId, profileRefId, instructionEntity.getId(), interactionId);
      } else {
        log.debug("balance check NOT required as there are no DEBIT transactions");
        isBalanceAvailable = true;
      }

      /*
       * Part D -- Update transaction status to reflect result
       * If <em>successful</em>, then status = PENDING
       * otherwise <em>failure</em>, then status = FAILED
       */

      insStatus = setInstructionStatus(isBalanceAvailable);
      transStatus = setTransactionStatus(isBalanceAvailable);

      log.debug("updateInitiateTransactions before instructionId {} transStatus = {} balanceAvailable = {}", instructionEntity.getId(), transStatus,
          isBalanceAvailable);

      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
      Instant instant = Instant.now();

      // Convert to ZonedDateTime
      ZonedDateTime zdt = instant.atZone(ZoneId.of("UTC"));

      // Format it to desired string format
      String formattedString = zdt.format(formatter);

      // Parse the string to LocalDateTime
      LocalDateTime updateTime = LocalDateTime.parse(formattedString, formatter);

      //Update InitiateTransactions with new status in DB
      List<TransactionEntity> transactionNewList = updateInitiateTransactions(instructionEntity.getId(), transStatus, initiateTransactions, updateTime);

      // update the instruction status to reflect transaction status
      instructionEntity.setStatus(insStatus);

      // persist instruction status updates
      instructionEntity = instructionRepository.save(instructionEntity);
      log.debug("updated instruction ref_id = {} with status = {} ",
          instructionEntity.getInstructionRefId(), instructionEntity.getStatus());

      instructionEntity.setTransactions(transactionNewList);
      instructionEntity.setUpdatedDateTime(updateTime);
      log.debug("updated instruction ref_id = {} with status = {} ",
          instructionEntity.getInstructionRefId(), instructionEntity.getStatus());

    } catch (ConstraintViolationException ex) {
      log.warn("constraint violation exception on saving instruction", ExceptionUtil.message(ex));
      throw new DuplicateInstructionException(Messages.DUPLICATE_INSTRUCTION);
    } catch (DataIntegrityViolationException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
//      log.warn("data integrity violation exception on saving instruction", ExceptionUtil.message(ex));
//      throw new DuplicateInstructionException(Messages.DUPLICATE_INSTRUCTION);
    } catch (IllegalArgumentException ex) {
      log.warn("illegal argument exception on initiating transaction", ExceptionUtil.message(ex));
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (InvalidFieldException ex) {
      throw new InvalidFieldException(ex.getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {

      log.error("unexpected exception on saving instruction", ex.getMessage());

      throw new Exception(ex.getMessage());
    }
    return instructionEntity;

  }

  @PerfLogger
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Instruction initiateInstruction(Instruction instruction, String profileRefId, String accountRefId, String interactionId) throws Exception {

    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */

    Instruction instructionResponse = null;
    try {
      // retrieve, null check and status check
      Boolean profileStatus = validationService.isProfileAvailable(profileRefId, interactionId);
      if (!profileStatus) {
        throw new InactiveStatusException(Messages.PROFILE_WAS_NOT_ENABLED);
      }

      // retrieve and null check
      Account account = validationService.getAccount(accountRefId, profileRefId, interactionId);
      String accountStatus = account.getStatus().toString();

      // check status: for INITIATE, status MUST be ACTIVE
      if (!accountStatus.equals(APICommonUtilConstant.ACTIVE)) {
        throw new InactiveStatusException(Messages.ACCOUNT_WAS_NOT_ACTIVE);
      }

      // Check for duplicate transactions before proceeding
      if (instruction.getTransactions() != null && !instruction.getTransactions().isEmpty()) {

        UUID profileUuid = toUuid(profileRefId, "profileRefId");
        UUID accountUuid = toUuid(accountRefId, "accountRefId");

        List<String> incomingRefs = instruction.getTransactions().stream()
            .map(Transaction::getTransactionRefId) // already String
            .toList();

        List<String> duplicates = transactionRepository
            .findExistingTransactionRefs(profileUuid, accountUuid, incomingRefs);

        if (!duplicates.isEmpty()) {
          log.warn("Duplicate transaction(s) {}", duplicates);
          throw new DuplicateTransactionException(Messages.DUPLICATE_TRANSACTION);
        }
      }

      InstructionEntity instructionEntity = createInstruction(account, instruction, profileRefId, accountRefId, interactionId);

      // return result
      instructionResponse = transactionMapper.fromInstructionEntityToInstruction(instructionEntity);
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (InactiveStatusException ex) {
      throw new InactiveStatusException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (DataIntegrityViolationException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getMessage());
    } catch (DuplicateTransactionException | DuplicateInstructionException ex) {
      throw ex; // Let the exception handler handle this
    } catch (InvalidFieldException ex) {
      throw new InvalidFieldException(ex.getMessage());
    } catch (IllegalArgumentException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
    return instructionResponse;

  }

  private static UUID toUuid(String id, String field) throws InvalidFieldException {
    try {
      return UUID.fromString(id);
    } catch (IllegalArgumentException ex) {
      throw new InvalidFieldException(field + " must be a valid UUID");
    }
  }

  /**
   * {{@inheritDoc}}
   */
  @Override
  public Instruction initiateReserve(Instruction instruction, String profileRefId,
      String accountRefId, String interactionId) throws Exception {

    InstructionEntity instructionEntity;

    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */
    try {
      validationService.validateProfileAndAccount(accountRefId, profileRefId, interactionId);

      /*
       * Part B -- POST the Internal Transaction(PREFUND RESERVE or CORRECTION)
       */

      List<Transaction> transactions = instruction.getTransactions();
      List<TransactionEntity> transactionEntities = new ArrayList<>();
      InstructionStatus insStatus = InstructionStatus.POSTED;
      instruction.setProfileRefId(profileRefId);
      instruction.setAccountRefId(accountRefId);
      instruction.setStatus(insStatus);
      instruction.setTransactions(null);

      instructionEntity = transactionMapper.fromInstructionToInstructionEntity(instruction);

      for (Transaction t : transactions) {

        t.setProfileRefId(profileRefId);
        t.setAccountRefId(accountRefId);
        t.setStatus(TransactionStatus.POSTED);
        t.setAcceptanceDateTime(DateUtils.offsetDateTime());
        t.setEffectiveDateTime(DateUtils.offsetDateTime());
        t.setFinalizationDateTime(DateUtils.offsetDateTime());
        t.setTransactionHold(TransactionHoldType.INSTANT);
        TransactionEntity transactionEntity = transactionMapper.fromTransactionToTransactionEntity(t);
        transactionEntity.setInstruction(instructionEntity);
        // add transaction
        transactionEntities.add(transactionEntity);
      }

      //add transactions to instruction
      instructionEntity.setTransactions(transactionEntities);
      // save the instruction to repo
      instructionEntity = instructionRepository.save(instructionEntity);
    } catch (ConstraintViolationException ex) {
      log.warn("constraint violation exception on saving instruction", ExceptionUtil.message(ex));
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (IllegalArgumentException ex) {
      log.warn("illegal argument exception on initiating prefund reserve or internal adjustment", ex);
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (DataIntegrityViolationException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      log.error("unexpected exception on saving instruction", ex.getMessage());
      throw new Exception(ex.getMessage());
    }
    return transactionMapper.fromInstructionEntityToInstruction(instructionEntity);

  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public Instruction commitTransaction(String instructionRefId, String profileId, String accountId, String interactionId) throws Exception {

    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */

    Instruction response = null;
    try {
      validationService.validateProfileAndAccount(accountId, profileId, interactionId);

      /*
       * Part B -- Locate existing instruction.
       */
      InstructionEntity instructionEntity = instructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
          UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId);

      // check if exists
      if (Objects.isNull(instructionEntity)) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }
      if (Objects.nonNull(instructionEntity)) {
        List<TransactionEntity> transactions = instructionEntity.getTransactions();

        for (TransactionEntity t : transactions) {
          log.debug("processing transaction_ref_id = {}, status = {}", t.getTransactionRefId(), t.getStatus());

          if (TransactionStatus.PENDING.equals(t.getStatus())) {
            t.setStatus(TransactionStatus.POSTED);
            t.setFinalizationDateTime(DateUtils.offsetDateTime().toLocalDateTime());
            log.debug("updated transaction from PENDING to POSTED, new status = {}", t.getStatus());
          } else if (TransactionStatus.POSTED.equals(t.getStatus())) {
            log.debug("transaction already in POSTED status, no changes needed");
          } else {
            throw new InvalidStatusTransitionException(String.format(Messages.TRANSACTION_CURRENT_STATUS, t.getStatus()));
          }
        }

        if (InstructionStatus.PENDING.equals(instructionEntity.getStatus()) || InstructionStatus.POSTED.equals(instructionEntity.getStatus())) {
          instructionEntity.setStatus(InstructionStatus.POSTED);
        } else {
          throw new InvalidStatusTransitionException(String.format(Messages.TRANSACTION_CURRENT_STATUS, instructionEntity.getStatus()));
        }

        // persist
        instructionRepository.save(instructionEntity);

        // response
        response = transactionMapper.fromInstructionEntityToInstruction(instructionEntity);
      } else {
        throw new InvalidFieldException(Messages.INSTRUCTION_REF_ID_NOT_FOUND);
      }
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (InvalidStatusTransitionException ex) {
      throw new InvalidStatusTransitionException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
    return response;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  @Transactional
  public void rollbackTransaction(String instructionRefId, String profileId, String accountId, String interactionId)
      throws Exception {
    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */

    try {
      validationService.validateProfileAndAccount(accountId, profileId, interactionId);

      /*
       * Part B -- Locate existing instruction, and Add instruction & transactions in INIT_PENDING status, ensures these transactions are reflected in case of concurrent postings.
       */
      InstructionEntity instructionEntity = readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
          UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId);

      // check if exists
      if (Objects.isNull(instructionEntity)) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }

      if (Objects.nonNull(instructionEntity)) {
        List<TransactionEntity> transactions = instructionEntity.getTransactions();

        for (TransactionEntity t : transactions) {
          log.debug("rollback-->processing transaction_ref_id = {}, status = {}", t.getTransactionRefId(), t.getStatus());

          if (t.getStatus() != TransactionStatus.PENDING && t.getStatus() != TransactionStatus.ROLLBACKED) {
            throw new InvalidStatusTransitionException(String.format(Messages.TRANSACTION_CURRENT_STATUS, t.getStatus()));
          }

          if (TransactionStatus.PENDING.equals(t.getStatus())) {
            t.setStatus(TransactionStatus.ROLLBACKED);
            t.setFinalizationDateTime(DateUtils.offsetDateTime().toLocalDateTime());
            log.debug("rollback-->updated transaction from PENDING to ROLLBACKED, new status = {}", t.getStatus());
          } else if (TransactionStatus.ROLLBACKED.equals(t.getStatus())) {
            log.debug("rollback-->transaction already in ROLLBACKED status, no update finalized_date_time");
          }
        }

        if (InstructionStatus.PENDING != instructionEntity.getStatus() && InstructionStatus.ROLLBACKED != instructionEntity.getStatus()) {
          throw new InvalidStatusTransitionException(String.format(Messages.TRANSACTION_CURRENT_STATUS, instructionEntity.getStatus()));
        } else {
          instructionEntity.setStatus(InstructionStatus.ROLLBACKED);
        }

        // persist
        instructionRepository.save(instructionEntity);
      } else {
        throw new InvalidFieldException(Messages.INSTRUCTION_REF_ID_NOT_FOUND);
      }
    } catch (InvalidStatusTransitionException ex) {
      throw new InvalidStatusTransitionException(ex.getMessage());
    } catch (InvalidFieldException ex) {
      throw new InvalidFieldException(ex.getMessage());
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
  }

  /**
   * Utility function for inserting initial records for Phase 1 :: Initiate.
   * <em>Insert transactions with status of INIT_PENDING</em>
   *
   * @param transactions      list of transactions to insert
   * @param accountId         unique account identifier
   * @param profileId         unique profile identifier
   * @param interactionId     unique interaction identifier
   * @param instructionEntity instruction
   */
  @PerfLogger
  private List<TransactionEntity> insertInitiateTransactions(Account account, List<Transaction> transactions, String accountId, String profileId,
      String interactionId,
      InstructionEntity instructionEntity) throws Exception {
    List<TransactionEntity> transactionEntities = new ArrayList<>();

    // Retrieve <em>Fund Hold Days</em> from Account API
    Integer fundHoldDays = account.getOptions().getFundHoldDays();

    // shielded debug logging
    if (log.isDebugEnabled()) {
      log.debug("new instruction record; ref_id = {}, profile_ref_id = {}, account_ref_id = {}, payment_rail = {}",
          instructionEntity.getInstructionRefId(), instructionEntity.getProfileRefId(), instructionEntity.getAccountRefId(), instructionEntity.getPaymentRail());
    }

    // Update transaction with relevant details (augments mapped data in DTO)
    for (Transaction t : transactions) {

      // shielded debug logging
      if (log.isDebugEnabled()) {
        log.debug("new transaction record; ref_id = {}, payment_rail = {}, payment_category = {}, amount = {}, acceptance_date_time = {}, due_date_time = {}",
            t.getTransactionRefId(), instructionEntity.getPaymentRail(), t.getPaymentCategory(), t.getAmount(), t.getAcceptanceDateTime(),
            t.getDueDateTime());
      }

      // profile and account identifier
      t.setProfileRefId(profileId);
      t.setAccountRefId(accountId);

      // status is INIT_PENDING
      t.setStatus(TransactionStatus.INIT_PENDING);

      OffsetDateTime effectiveDateTime = null;
      TransactionHoldType transactionHoldType = null;

      // Determine transaction flow based on payment_category
      TransactionFlowType transactionFlow = determineTransactionFlow(instructionEntity.getPaymentRail(), t.getPaymentCategory());
      if (null != transactionFlow) {
        t.setTransactionFlow(transactionFlow);
      } else {
        throw new InvalidFieldException(Messages.INVALID_PAYMENT_CATEGORY);
      }

      // Conditional effective date based on DEBIT or CREDIT transaction flow type.
      if (TransactionFlowType.DEBIT.equals(t.getTransactionFlow())) {
        // all debits are effective immediately, and impact the balance right away
        if (t.getAcceptanceDateTime().isBefore(DateUtils.offsetDateTime())) {
          effectiveDateTime = DateUtils.offsetDateTime();
        } else {
          effectiveDateTime = t.getAcceptanceDateTime();
        }
        transactionHoldType = TransactionHoldType.INSTANT;

      } else if (TransactionFlowType.CREDIT.equals(t.getTransactionFlow())) {
        // credits are effective on due date + any number of days funds are to be held

        // no due date defined, or due date is earlier or equal to acceptance date time
        if (t.getDueDateTime() == null || (t.getDueDateTime().isBefore(t.getAcceptanceDateTime()) || t.getDueDateTime().equals(t.getAcceptanceDateTime()))) {
          if (fundHoldDays > 0) {
            // acceptance date and time becomes the base date for effective date and time calculation
            // Converting UTC to ET Time
            LocalDateTime localDateTime = t.getAcceptanceDateTime().atZoneSameInstant(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO)).toLocalDateTime();

            //change the time to 9AM
            LocalDateTime startOfDay9AM = localDateTime.toLocalDate().atStartOfDay().withHour(9).withMinute(0).withSecond(0).withNano(0);

            //Condition to check if acceptance date time is after 9AM in ET
            if (localDateTime.isAfter(startOfDay9AM)) {
              // Change the time to 9AM in ET  and add fund hold days plus one extra day
              localDateTime = startOfDay9AM.plusDays(fundHoldDays + 1);
            } else {
              // Change the time to 9AM in ET  and add fund hold days
              localDateTime = startOfDay9AM.plusDays(fundHoldDays);
            }

            ZonedDateTime zonedDateTimeET = localDateTime.atZone(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO));
            ZonedDateTime zonedDateTimeUTC = zonedDateTimeET.withZoneSameInstant(ZoneId.of(APICommonUtilConstant.UTC));

            //Effective date time in UTC zone.
            effectiveDateTime = OffsetDateTime.of(zonedDateTimeUTC.toLocalDateTime(), ZoneOffset.UTC);
            transactionHoldType = TransactionHoldType.HOLD;

          } else {
            effectiveDateTime = t.getAcceptanceDateTime();
            if (effectiveDateTime.isAfter(DateUtils.offsetDateTime())) {
              transactionHoldType = TransactionHoldType.HOLD;
            } else {
              transactionHoldType = TransactionHoldType.INSTANT;
            }
          }
        }
        // due date is defined and due date is after acceptance date
        else {
          if (fundHoldDays > 0) {
            // due date and time becomes the base date for effective date and time calculation
            LocalDateTime localDateTime = t.getDueDateTime().atZoneSameInstant(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO)).toLocalDateTime();

            //change the time to 9AM
            LocalDateTime startOfDay9AM = localDateTime.toLocalDate().atStartOfDay().withHour(9).withMinute(0).withSecond(0).withNano(0);

            //Condition to check if due date time is after 9AM in ET
            if (localDateTime.isAfter(startOfDay9AM)) {
              // Change the time to 9AM in ET  and add fund hold days plus one extra day
              localDateTime = startOfDay9AM.plusDays(fundHoldDays + 1);
            } else {
              // Change the time to 9AM in ET  and add fund hold days
              localDateTime = startOfDay9AM.plusDays(fundHoldDays);
            }
            ZonedDateTime zonedDateTimeET = localDateTime.atZone(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO));
            ZonedDateTime zonedDateTimeUTC = zonedDateTimeET.withZoneSameInstant(ZoneId.of(APICommonUtilConstant.UTC));

            //Effective date time in UTC zone.
            effectiveDateTime = OffsetDateTime.of(zonedDateTimeUTC.toLocalDateTime(), ZoneOffset.UTC);
            transactionHoldType = TransactionHoldType.HOLD;
          } else {
            effectiveDateTime = t.getDueDateTime();
            if (effectiveDateTime.isAfter(DateUtils.offsetDateTime())) {
              transactionHoldType = TransactionHoldType.HOLD;
            } else {
              transactionHoldType = TransactionHoldType.INSTANT;
            }
          }
        }
      }

      // ensure the effective date is always in the FUTURE or CURRENT, never the past.
      if (effectiveDateTime.isBefore(DateUtils.offsetDateTime())) {
        effectiveDateTime = DateUtils.offsetDateTime();
      }
      t.setEffectiveDateTime(effectiveDateTime);
      t.setTransactionHold(transactionHoldType);

      // shielded debug logging
      if (log.isDebugEnabled()) {
        log.debug("effective_date_time = {}, transaction_hold = {}", t.getEffectiveDateTime(), t.getTransactionHold());
      }

      // transformation layer -- DTO to entity (persistence)
      TransactionEntity transactionEntity = transactionMapper.fromTransactionToTransactionEntity(t);
      transactionEntity.setInstruction(instructionEntity);

      // add transaction
      transactionEntities.add(transactionEntity);
    }
    ;

    // (bulk) update all associated transactions

    try {
      transactionRepository.saveAll(transactionEntities);
    } catch (ConstraintViolationException ex) {

      log.warn("constraint violation exception on saving transaction", ExceptionUtil.message(ex));

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (DataIntegrityViolationException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (IllegalArgumentException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {

      log.error("unexpected exception on saving transaction", ex.getMessage());

      throw new Exception(ex.getMessage());
    }
    log.debug("inserted {} transaction(s) with status = {}", transactionEntities.size(), transactionEntities.get(0).getStatus());
    return transactionEntities;
  }

  /**
   * Utility function for finalizing records for  Phase 1 :: Initiate.
   * <em>Insert transactions with status of PENDING or FAILED</em>
   *
   * @param instructionId unique instruction identifier
   * @param transStatus   transaction status
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  private List<TransactionEntity> updateInitiateTransactions(Integer instructionId, TransactionStatus transStatus,
      List<TransactionEntity> transactionEntities, LocalDateTime updateTime)
      throws Exception {
    try {
      transactionEntities.forEach(t -> {
        t.setStatus(transStatus);
        t.setUpdatedDateTime(updateTime);
        if (TransactionStatus.FAILED.equals(transStatus)) {
          t.setFinalizationDateTime(DateUtils.offsetDateTime().toLocalDateTime());
        }
      });

      // (bulk) update all associated transactions
      transactionEntities = transactionRepository.saveAll(transactionEntities);
      log.debug("updateInitiateTransactions after total {} transaction(s) with status = {} time = {}", transactionEntities.size(),
          transactionEntities.get(0).getStatus(),
          updateTime);

      return transactionEntities;
    } catch (ConstraintViolationException | DataIntegrityViolationException | IllegalArgumentException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
  }

  /**
   * Utility function to set the instruction status based on isBalanceAvailable flag for Phase 1 :: Initiate.
   *
   * @param isBalanceAvailable
   * @return
   */
  private InstructionStatus setInstructionStatus(boolean isBalanceAvailable) {
    InstructionStatus newStatus;
    if (isBalanceAvailable) {
      newStatus = InstructionStatus.PENDING;
    } else {
      newStatus = InstructionStatus.FAILED;
    }
    return newStatus;
  }

  /**
   * Utility function to set the transaction status based on isBalanceAvailable flag for Phase 1 :: Initiate.
   *
   * @param isBalanceAvailable
   * @return
   */
  private TransactionStatus setTransactionStatus(boolean isBalanceAvailable) {
    TransactionStatus newStatus;
    if (isBalanceAvailable) {
      newStatus = TransactionStatus.PENDING;
    } else {
      newStatus = TransactionStatus.FAILED;
    }
    return newStatus;
  }

  /**
   * Utility function to calculate the account balance, including current transactions and (past) historical.
   *
   * @param account
   * @param accountId
   * @param profileId
   * @param instructionId
   * @param interactionId
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  private boolean calculateAccountBalance(Account account, String accountId, String profileId, Integer instructionId, String interactionId)
      throws InvalidFieldException {

    // retrieve from persistence
    Optional<BalanceEntity> balanceSnapshot = getBalanceSnapshot(accountId, profileId);

    // totals
    BigDecimal balanceSnapshotTotal;
    BigDecimal balanceSnapshotPendingTotal;
    Optional<BigDecimal> totalSumB = null;

    //Check whether previous snapshot is available
    if (balanceSnapshot.isPresent()) {
      // balance snapshot exists
      BalanceEntity entity = balanceSnapshot.get();
      balanceSnapshotTotal = entity.getTotalAmount();
      // Get the pending amount from snapshot
      balanceSnapshotPendingTotal = entity.getTotalPendingAmount() != null ?
          entity.getTotalPendingAmount() : BigDecimal.ZERO;

      log.info("balance snapshot retrieved [{},{}] with pending amount: {}",
          entity.getEffectiveFromDateTime(), entity.getEffectiveToDateTime(), balanceSnapshotPendingTotal);

      // Retrieve sum of all the credits and debits from persistence (from AFTER balance snapshot)
      totalSumB = getTotalAmount(accountId, profileId, instructionId, balanceSnapshot.get().getEffectiveToDateTime());

    } else {
      balanceSnapshotTotal = BigDecimal.ZERO;
      balanceSnapshotPendingTotal = BigDecimal.ZERO;

      log.info("no existing balance snapshot");

      // Retrieve sum of all the credits and debits from persistence (since today only)
      totalSumB = getTotalAmount(accountId, profileId, instructionId);
    }

    BigDecimal totalSum = totalSumB.isPresent() ? totalSumB.get() : BigDecimal.ZERO;

    // Calculate effective balance using the formula:
    // Effective Balance = snapshot.total_amount + snapshot.total_pending_amount + SUM(New Transactions) - SUM(Updated Transactions)
    BigDecimal effectiveBalance = balanceSnapshotTotal
        .add(balanceSnapshotPendingTotal)  // Add the pending amount from snapshot
        .add(totalSum);                    // This already contains (new transactions - updated transactions)

    // ensure DEBUG mode before running the below
    if (log.isDebugEnabled()) {
      log.debug("SNAPSHOT_TOTAL + SNAPSHOT_PENDING + SUM(NEW_TXN-UPDATED_TXN) = BALANCE | {} + {} + {} = {}",
          balanceSnapshotTotal.doubleValue(), balanceSnapshotPendingTotal.doubleValue(),
          totalSum.doubleValue(), effectiveBalance.doubleValue());
    }

    BigDecimal overDraftAmount = null;
    if (account.getOptions() == null || account.getOptions().getOverdraftAmount() == null) {
      // fallback, default to 0 if none defined
      overDraftAmount = BigDecimal.ZERO;
    } else {
      overDraftAmount = account.getOptions().getOverdraftAmount();
      log.debug("overdraft = {}", overDraftAmount);
    }

    // validate
    Boolean isSufficientBalance = false;
    if (effectiveBalance.compareTo(BigDecimal.ZERO) >= 0) {
      isSufficientBalance = true;
    } else {
      isSufficientBalance = effectiveBalance.add(overDraftAmount).compareTo(BigDecimal.ZERO) >= 0;
    }
    log.debug("isSufficientBalance = {}", isSufficientBalance);

    return isSufficientBalance;
  }

  /**
   * Utility function to retrieve sum of all credits for a specific profile and account. This includes instructions from effectiveToDateTime in the balance snapshot to current
   *
   * @param accountId
   * @param profileId
   * @param instructionId
   * @param effectiveToDateTime
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  @Override
  public Optional<BigDecimal> getTotalAmount(String accountId, String profileId, Integer instructionId, LocalDateTime effectiveToDateTime)
      throws InvalidFieldException {
    log.debug("start call getTotalAmount for account {}", accountId);

    UUID accountUuid = UUID.fromString(accountId);
    UUID profileUuid = UUID.fromString(profileId);
    LocalDateTime now = DateUtils.offset();

    Optional<BigDecimal> totalChangeOpt;

    if (!transactionProperty.getUseStoreProcedureCalculateSum()) {
      // --- PURE JPA IMPLEMENTATION (NOW FULLY MATCHES SP) ---
      log.debug("Calculating total amount using JPA queries (rollbacks ignored).");

      // 1. Sum of NEW transactions since the last snapshot (EXCLUDING ROLLBACKED)
      Optional<BigDecimal> sumNewTransactionsOpt = transactionRepository.sumAmountByDateRangeInstructionId(
          accountUuid, profileUuid, effectiveToDateTime, now, instructionId
      );

      // Calculate the 33-day lookback window for the performance filter
      LocalDateTime lookbackStartTime = effectiveToDateTime.minusDays(33);

      // 2. Sum of transactions that were PENDING at snapshot time but are now ROLLBACKED
      // This now passes all required parameters to the corrected repository method.
      Optional<BigDecimal> pendingToRollbackedOpt = transactionRepository.sumPendingToRollbackedTransitionsSince(
          accountUuid, profileUuid, effectiveToDateTime, lookbackStartTime, instructionId
      );

      BigDecimal newTransactionsTotal = sumNewTransactionsOpt.orElse(BigDecimal.ZERO);
      BigDecimal pendingToRollbackedTotal = pendingToRollbackedOpt.orElse(BigDecimal.ZERO);

      // 3. Calculate total change: (new transactions) - (pending→rollbacked transitions)
      BigDecimal totalChange = newTransactionsTotal.subtract(pendingToRollbackedTotal);

      log.debug("Total change calculation (JPA): newTransactions ({}) - pendingToRollbacked ({}) = {}",
          newTransactionsTotal, pendingToRollbackedTotal, totalChange);

      totalChangeOpt = Optional.of(totalChange);

    } else {
      // --- STORED PROCEDURE IMPLEMENTATION ---
      log.debug("Calculating total amount using stored procedure.");

      totalChangeOpt = transactionRepository.sumAmountByDateRangeInstructionId_SP(
          accountUuid, profileUuid, effectiveToDateTime, now, instructionId
      );

      log.debug("Total change calculation (SP) returned: {}", totalChangeOpt.orElse(BigDecimal.ZERO));
    }

    log.debug("end call getTotalAmount");
    return totalChangeOpt;
  }

  /**
   * Utility function to retrieve sum of all credits and debits for a specific profile and account. This includes only  instructions from yesterday to current
   *
   * @param accountId
   * @param profileId
   * @param instructionId
   * @return
   */
  @PerfLogger
  @Override
  public Optional<BigDecimal> getTotalAmount(String accountId, String profileId, Integer instructionId) {
    log.debug("start call getTotalAmount for account {}", accountId);

    LocalDateTime effectiveDateTimeStart = DateUtils.startOfDay(1);

    // call the other getTotalAmount method to avoid duplicating logic
    try {
      return getTotalAmount(accountId, profileId, instructionId, effectiveDateTimeStart);
    } catch (InvalidFieldException e) {
      // This exception is unlikely here, but handle it for safety
      log.error("Unexpected InvalidFieldException", e);
      return Optional.of(BigDecimal.ZERO);
    }
  }


  @PerfLogger
  @Override
  public Optional<BalanceEntity> getBalanceSnapshot(String accountId, String profileId) throws InvalidFieldException {
    Optional<BalanceEntity> result = readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(
        UUID.fromString(accountId), UUID.fromString(profileId));

    // result
    return result;
  }


  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public Instruction getInstruction(String profileRefId, String accountRefId, String instructionRefId, String interactionId)
      throws Exception {

    InstructionEntity instructionEntity = null;

    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */

    try {
      validationService.validateProfileAndAccount(accountRefId, profileRefId, interactionId);


      /*
       * Part B -- Retrieve instruction using profile ID, account ID and instruction reference ID.
       */

      instructionEntity = readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
          UUID.fromString(profileRefId), UUID.fromString(accountRefId), instructionRefId);
      if (instructionEntity == null) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }
      List<TransactionEntity> transactions =
          instructionEntity.getTransactions().stream().filter(t -> t.getPaymentCategory() != PaymentCategoryType.REVERSAL).
              collect(Collectors.toList());

      instructionEntity.setTransactions(transactions);

    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
    return transactionMapper.fromInstructionEntityToInstruction(instructionEntity);
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public Transaction getTransaction(String instructionRefId, String transactionRefId, String profileRefId, String accountRefId, String interactionId)
      throws Exception {

    List<TransactionEntity> ts;

    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */

    try {
      validationService.validateProfileAndAccount(accountRefId, profileRefId, interactionId);
      /*
       * Part B -- Retrieve specific transaction using profile ID, account ID, instruction reference ID and transaction reference ID.
       */
      ts = readTransactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(instructionRefId,
          transactionRefId, UUID.fromString(profileRefId), UUID.fromString(accountRefId));

      // check if exists
      if (Objects.isNull(ts) || ts.isEmpty()) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
    return transactionMapper.fromTransactionEntityToTransaction(ts.get(0));
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public void reverseTransaction(String instructionRefId, String transactionRefId, String profileRefId, String accountRefId, String interactionId)
      throws Exception {
    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */

    try {
      validationService.validateProfileAndAccount(accountRefId, profileRefId, interactionId);


      /*
       * Part B -- Update the existing transaction status with Reversed
       */

      List<TransactionEntity> ts = readTransactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(instructionRefId,
          transactionRefId, UUID.fromString(profileRefId), UUID.fromString(accountRefId));

      // check if exists
      if (Objects.isNull(ts) || ts.isEmpty()) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }
      TransactionEntity transaction = ts.get(0);

      // check if status is POSTED
      if (TransactionStatus.POSTED != transaction.getStatus() && TransactionStatus.REVERSED != transaction.getStatus()) {
        throw new InvalidStatusTransitionException(Messages.TRANSACTION_STATUS_NOT_POSTED);
      }
      if (transaction.getStatus() == TransactionStatus.POSTED) {
        transaction.setStatus(TransactionStatus.REVERSED);
        transaction.setFinalizationDateTime(DateUtils.offsetDateTime().toLocalDateTime());
        TransactionEntity newTransaction = transactionRepository.save(transaction);

        /*
         * Part C -- Post the reversed transaction as new transaction with Payment Category as Reversal and Transaction Flow
         */
        postNewTransaction(newTransaction);
      }
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (InvalidStatusTransitionException ex) {
      throw new InvalidStatusTransitionException(ex.getMessage());
    } catch (InvalidFieldException ex) {
      throw new InvalidFieldException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
  }

  /**
   * Creates a new transaction with status set to REVERSED for a previously reversed transaction. The transaction flow is reversed (credit to debit or vice versa), and the amount is adjusted
   * accordingly.
   *
   * @param transactionEntity The original transaction being reversed.
   */
  @PerfLogger
  private void postNewTransaction(TransactionEntity transactionEntity) {
    // Initialize a new transaction entity
    TransactionEntity newTransaction = new TransactionEntity();

    // Convert the original transaction entity to a transaction object and then back to entity
    Transaction transaction = transactionMapper.fromTransactionEntityToTransaction(transactionEntity);
    newTransaction = transactionMapper.fromTransactionToTransactionEntity(transaction);

    // Set properties for the new reversed transaction
    newTransaction.setTransactionRefId(UUID.randomUUID().toString());
    newTransaction.setInstruction(transactionEntity.getInstruction());
    newTransaction.setId(null);  // Ensure no ID is set for the new transaction
    newTransaction.setRelatedId(transactionEntity.getId());
    newTransaction.setPaymentCategory(PaymentCategoryType.REVERSAL);

    // Update the effective date for the new transaction based on the original transaction
    updateEffectiveDate(newTransaction, transactionEntity);

    // Adjust the transaction flow and amount based on the original transaction's flow
    updateTransactionFlowAndAmount(newTransaction, transaction);

    // Save the new reversed transaction to the repository
    transactionRepository.save(newTransaction);
  }

  /**
   * Updates the effective date for the new transaction. If the original transaction's effective date is after the current UTC offset date and time, it is used. Otherwise, the current UTC offset date
   * and time (retrieved by DateUtils.offset()) is set as the effective date.
   *
   * @param newTransaction      The new transaction to update the effective date for.
   * @param existingTransaction The original transaction used to determine the effective date.
   */
  private void updateEffectiveDate(TransactionEntity newTransaction, TransactionEntity existingTransaction) {
    // Use the original effective date if it is later than the current UTC offset time
    if (existingTransaction.getEffectiveDateTime() != null && existingTransaction.getEffectiveDateTime().isAfter(DateUtils.offset())) {
      newTransaction.setEffectiveDateTime(existingTransaction.getEffectiveDateTime());
    } else {
      // Otherwise, use the current UTC offset time as the effective date
      newTransaction.setEffectiveDateTime(DateUtils.offset());
    }
  }

  /**
   * Adjusts the transaction flow and amount for the reversed transaction. Reverses the flow (credit to debit or vice versa) and negates or takes the absolute value of the amount.
   *
   * @param newTransaction The new transaction being created.
   * @param transaction    The original transaction used to determine flow and amount.
   */
  private void updateTransactionFlowAndAmount(TransactionEntity newTransaction, Transaction transaction) {
    if (newTransaction.getTransactionFlow() == TransactionFlowType.CREDIT) {
      // Reverse a credit to a debit with a negated amount
      newTransaction.setTransactionFlow(TransactionFlowType.DEBIT);
      newTransaction.setAmount(transaction.getAmount().negate());
    } else if (newTransaction.getTransactionFlow() == TransactionFlowType.DEBIT) {
      // Reverse a debit to a credit with an absolute amount
      newTransaction.setTransactionFlow(TransactionFlowType.CREDIT);
      newTransaction.setAmount(transaction.getAmount().abs());
    }
  }

  /**
   * Utility function to retrieve sum of all credits from yesterday to today for a specific profile and account.
   *
   * @param accountId unique account identifier
   * @param profileId unique profile identifier
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  @Override
  public Optional<BigDecimal> getTotalAmount(String accountId, String profileId) throws InvalidFieldException {
    Optional<BigDecimal> result = readTransactionRepository.sumAmountByDateRange(UUID.fromString(accountId), UUID.fromString(profileId),
        DateUtils.startOfDay(1), DateUtils.offset());

    // result
    return Optional.of(result.orElse(BigDecimal.ZERO.setScale(2)));
  }


  /**
   * Utility function to retrieve sum of all credits from effectiveToDateTime in the latest balance snapshot to till date for a specific profile and account.
   *
   * @param accountId
   * @param profileId
   * @param effectiveToDateTime
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  @Override
  public Optional<BigDecimal> getTotalAmount(String accountId, String profileId, LocalDateTime effectiveToDateTime) throws InvalidFieldException {
    // result
    return readTransactionRepository.sumAmountByDateRange(UUID.fromString(accountId), UUID.fromString(profileId),
        effectiveToDateTime, DateUtils.offset());
  }

  /**
   * Utility function to calculate effective balance (current balance + prefund reserve)
   *
   * @param accountId unique account identifier
   * @param profileId unique profile identifier
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  public BigDecimal getEffectiveBalance(String accountId, String profileId) throws InvalidFieldException {

    log.debug("calculating effective balance");

    Optional<BalanceEntity> balanceSnapshot = getBalanceSnapshot(accountId, profileId);
    BigDecimal balanceSnapshotTotal = balanceSnapshot.isPresent() ?
        balanceSnapshot.get().getTotalAmount() : BigDecimal.ZERO;
    BigDecimal balanceSnapshotPendingTotal = balanceSnapshot.isPresent() &&
        balanceSnapshot.get().getTotalPendingAmount() != null ?
        balanceSnapshot.get().getTotalPendingAmount() : BigDecimal.ZERO;

    Optional<BigDecimal> totalAmountB;

    if (balanceSnapshot.isEmpty()) {
      // When no snapshot exists, we need to get ALL transactions from the beginning of time
      // We should use a very old date or get the latest instruction ID

      // Option 1: Use a far past date (e.g., epoch or a reasonable business start date)
      LocalDateTime veryOldDate = LocalDateTime.of(2000, 1, 1, 0, 0, 0);

      // Get the latest instruction ID for this account
      // You might need to add a method to get this, or use Integer.MAX_VALUE as a safe upper bound
      Integer latestInstructionId = Integer.MAX_VALUE; // or fetch the actual latest

      totalAmountB = getTotalAmount(accountId, profileId, latestInstructionId, veryOldDate);

    } else {
      // When snapshot exists, get changes since the snapshot
      // Need to get the latest instruction ID
      Integer latestInstructionId = Integer.MAX_VALUE; // or fetch the actual latest

      totalAmountB = getTotalAmount(accountId, profileId, latestInstructionId,
          balanceSnapshot.get().getEffectiveToDateTime());
    }

    BigDecimal totalAmount = totalAmountB.orElse(BigDecimal.ZERO);

    // Apply the complete formula including pending amount
    BigDecimal effectiveBalance = balanceSnapshotTotal
        .add(balanceSnapshotPendingTotal)
        .add(totalAmount);

    // ensure DEBUG mode before running the below
    if (log.isDebugEnabled()) {
      log.debug("SNAPSHOT_TOTAL + SNAPSHOT_PENDING + CHANGE = BALANCE | {} + {} + {} = {}",
          balanceSnapshotTotal.doubleValue(), balanceSnapshotPendingTotal.doubleValue(),
          totalAmount.doubleValue(), effectiveBalance.doubleValue());
    }

    return effectiveBalance;
  }

  /**
   * Utility function to retrieve sum of all prefund reserve credits from yesterday to today for a specific profile and account.
   *
   * @param accountId unique account identifier
   * @param profileId unique profile identifier
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  @Override
  public Optional<BigDecimal> getReserveTotalAmount(String accountId, String profileId) throws InvalidFieldException {
    Optional<BigDecimal> result = readTransactionRepository.getReserveAmount(UUID.fromString(accountId), UUID.fromString(profileId),
        DateUtils.startOfDay(1), DateUtils.offset());

    // result
    return Optional.of(result.orElse(BigDecimal.ZERO.setScale(2)));
  }

  /**
   * Utility function to retrieve sum of all prefund reserve credits from effectiveToDateTime in the latest balance snapshot to till date for a specific profile and account.
   *
   * @param accountId
   * @param profileId
   * @param effectiveToDateTime
   * @return
   * @throws InvalidFieldException
   */
  @Override
  public Optional<BigDecimal> getReserveTotalAmount(String accountId, String profileId, LocalDateTime effectiveToDateTime) {
    Optional<BigDecimal> result = readTransactionRepository.getReserveAmount(UUID.fromString(accountId), UUID.fromString(profileId),
        effectiveToDateTime, DateUtils.offset());

    // result
    return Optional.of(result.orElse(BigDecimal.ZERO.setScale(2)));
  }


  /**
   * Utility function to calculate effective prefund reserve balance for a specific profile and account.
   *
   * @param accountId unique account identifier
   * @param profileId unique profile identifier
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  public BigDecimal getEffectiveReserveBalance(String accountId, String profileId) throws InvalidFieldException {

    log.debug("calculating effective reserve balance");

    Optional<BalanceEntity> balanceSnapshot = getBalanceSnapshot(accountId, profileId);
    BigDecimal balanceSnapshotReserveTotal = balanceSnapshot.isPresent() ? balanceSnapshot.get().getTotalReserveAmount() : BigDecimal.ZERO;

    Optional<BigDecimal> reserveTotalB;
    //Check whether previous snapshot is available
    if (balanceSnapshot.isEmpty()) {
      // Retrieve sum of all the reserve credits and debits from persistence
      reserveTotalB = getReserveTotalAmount(accountId, profileId);
    } else {
      // Retrieve sum of all the reserve credits and debits from persistence
      reserveTotalB = getReserveTotalAmount(accountId, profileId, balanceSnapshot.get().getEffectiveToDateTime());
    }

    BigDecimal reserveTotal = reserveTotalB.orElse(BigDecimal.ZERO);

    BigDecimal effectiveReserveBalance = reserveTotal.add(balanceSnapshotReserveTotal);

    // ensure DEBUG mode before running the below
    if (log.isDebugEnabled()) {
      log.debug("SUM(CREDITS) - SUM(DEBITS) = BALANCE | {} = {}", reserveTotal.doubleValue(), effectiveReserveBalance.doubleValue());
    }

    // calculate effective Reserve balance
    return effectiveReserveBalance;
  }

  /**
   * Utility function to calculate prefund reserve amount used so far for a specific profile and account.
   *
   * @param accountId unique account identifier
   * @param profileId unique profile identifier
   * @return
   * @throws InvalidFieldException
   */
  @PerfLogger
  public BigDecimal getPrefundReserveUsedAmount(String accountId, String profileId) throws InvalidFieldException {

    BigDecimal effectiveBalance = getEffectiveBalance(accountId, profileId);
    log.info("effectiveBalance = {}", effectiveBalance);

    BigDecimal effectiveReserveBalance = getEffectiveReserveBalance(accountId, profileId);
    log.info("effectiveReserveBalance = {}", effectiveReserveBalance);

    BigDecimal prefundBalanceUsed = BigDecimal.ZERO;
    if (effectiveBalance.subtract(effectiveReserveBalance).compareTo(BigDecimal.ZERO) >= 0) {
      log.debug("effective account balance greater than reserve balance, reserve not in use");
      prefundBalanceUsed = effectiveReserveBalance;
    } else {
      prefundBalanceUsed = effectiveReserveBalance.subtract(effectiveBalance);
      log.debug("effective account balance less than reserve balance, reserve in use");
    }

    // returns prefund reserve amount used so far
    return prefundBalanceUsed;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public BigDecimal getPrefundReserveBalanceAmount(String accountId, String profileId) throws InvalidFieldException {

    BigDecimal effectiveReserveBalance = getEffectiveReserveBalance(accountId, profileId);

    // returns Prefund Reserve Balance
    return effectiveReserveBalance.subtract(getPrefundReserveUsedAmount(accountId, profileId));
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public BigDecimal getFundHoldAmount(String accountId, String profileId) {
    Optional<BigDecimal> result = readTransactionRepository.getFundHoldAmount(UUID.fromString(accountId), UUID.fromString(profileId),
        TransactionHoldType.HOLD, DateUtils.offset());
    return result.orElse(BigDecimal.ZERO.setScale(2));
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public RetrieveBalanceResponse retrieveAccountBalance(String profileRefId, String accountRefId,
      String interactionId, String overdraftAmount) throws InvalidFieldException {

    // Amount that was held
    BigDecimal fundHoldAmount = getFundHoldAmount(accountRefId, profileRefId);
    log.info("fundHoldAmount = {}", fundHoldAmount);

    // Effective balance is the subtraction of sum of all debits from sum of all credits.
    BigDecimal effectiveBalance = getEffectiveBalance(accountRefId, profileRefId);
    log.info("effectiveBalance = {}", effectiveBalance);

    // Effective reserve balance is the subtraction of  sum of all prefund reserve debits from sum of  all prefund reserve credits.
    BigDecimal effectiveReserveBalance = getEffectiveReserveBalance(accountRefId, profileRefId);
    log.info("effectiveReserveBalance = {}", effectiveReserveBalance);

    BigDecimal prefundBalanceUsed = BigDecimal.ZERO;
    //prefund reserve amount remaining
    if ((effectiveBalance.compareTo(effectiveReserveBalance) > 0) || (effectiveBalance.compareTo(effectiveReserveBalance) == 1)) {
      log.debug("effective account balance either greater or equal to  effective reserve balance, reserve not in use");
      prefundBalanceUsed = BigDecimal.ZERO;
    } else {
      prefundBalanceUsed = effectiveReserveBalance.subtract(effectiveBalance);
      log.debug("effective account balance less than reserve balance, reserve in use");
    }

    BigDecimal prefundReserveBalance = effectiveReserveBalance.subtract(prefundBalanceUsed);
    BigDecimal prefundReserveRemaining = BigDecimal.ZERO;
    if (prefundReserveBalance.compareTo(BigDecimal.ZERO) < 0) {
      prefundReserveRemaining = BigDecimal.ZERO;
    } else {
      prefundReserveRemaining = prefundReserveBalance;
    }
    log.info("prefund reserve balance remaining = {}", prefundReserveRemaining);

    // current balance
    BigDecimal accountBalance = effectiveBalance.subtract(prefundReserveBalance);
    log.info("account balance = {}", accountBalance);

    // Overdraft amount from Account API
    BigDecimal overdraft = new BigDecimal(overdraftAmount);
    BigDecimal overdraftRemaining;
    if (effectiveBalance.compareTo(BigDecimal.ZERO) < 0) {
      overdraftRemaining = overdraft.subtract(effectiveBalance.abs());
    } else {
      overdraftRemaining = overdraft;
    }

    log.info("overdraft balance left = {}", overdraftRemaining);

    RetrieveBalanceResponse response = new RetrieveBalanceResponse();
    response.setAccountBalance(accountBalance);
    response.setEffectiveOn(DateUtils.offsetDateTime());
    response.setFundHoldAmount(fundHoldAmount);
    response.setPrefundReserveAmount(prefundReserveRemaining);
    response.setOverdraftAmount(overdraftRemaining);
    response.setAvailableBalance(accountBalance.add(prefundReserveRemaining).add(overdraftRemaining));

    return response;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  @Transactional
  public Boolean rollbackScheduler(String instructionRefId, String profileId, String accountId, String interactionId) {
    /*
     * Part A -- Initial validation of profile and account before proceeding.
     */

    Boolean b = false;

    try {
      InstructionEntity instructionEntity = readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
          UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId);

      // check if exists
      if (Objects.isNull(instructionEntity)) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }

      if (Objects.nonNull(instructionEntity)) {
        List<TransactionEntity> transactions = instructionEntity.getTransactions();

        for (TransactionEntity t : transactions) {
          log.debug("processing transaction_ref_id = {}, status = {}", t.getTransactionRefId(), t.getStatus());

          if (t.getStatus() != TransactionStatus.PENDING) {
            throw new InvalidStatusTransitionException(Messages.TRANSACTION_STATUS_NOT_PENDING);
          } else {
            t.setStatus(TransactionStatus.ROLLBACKED_SYSTEM);
            t.setFinalizationDateTime(DateUtils.offsetDateTime().toLocalDateTime());
          }
        }

        if (InstructionStatus.PENDING != instructionEntity.getStatus()) {
          throw new InvalidStatusTransitionException(Messages.INSTRUCTION_STATUS_NOT_PENDING);
        } else {
          instructionEntity.setStatus(InstructionStatus.ROLLBACKED_SYSTEM);
        }

        // persist
        instructionRepository.save(instructionEntity);
        b = true;
      } else {
        throw new InvalidFieldException(Messages.INSTRUCTION_REF_ID_NOT_FOUND);
      }
    } catch (Exception ex) {
      log.info("Rollback failed for the instruction {} due to {}", instructionRefId, ex.getMessage());
    }

    return b;
  }

  /**
   * {@inheritDoc}
   */

  @PerfLogger
  @Override
  public List<Balance> searchBalanceSnapShots(String profileRefId, String accountRefId, String interactionId,
      LocalDateTime startTime, LocalDateTime endTime, Integer offset, Integer maxResponseItems) throws ResourceNotFoundException {
    log.info("search BalanceSnapshot History for profile_ref_id={}, account_ref_id={} startTime={} endTime={} offSet={} maxResponseItems={}",
        profileRefId, accountRefId, startTime, endTime, offset, maxResponseItems);

    int pageNumber = offset / maxResponseItems;

    Pageable pageable = PageRequest.of(pageNumber, maxResponseItems, Sort.by("createdDateTime").descending());

    List<BalanceEntity> balanceEntities;

    if (startTime != null && endTime != null) {
      balanceEntities = readBalanceRepository.findByAccountRefIdAndProfileRefIdAndEffectiveToDateTimeBetween(
          UUID.fromString(accountRefId), UUID.fromString(profileRefId), startTime, endTime, pageable);
    } else if (startTime != null) {
      balanceEntities = readBalanceRepository.findByAccountRefIdAndProfileRefIdAndEffectiveToDateTimeAfter(
          UUID.fromString(accountRefId), UUID.fromString(profileRefId), startTime, pageable);
    } else if (endTime != null) {
      balanceEntities = readBalanceRepository.findByAccountRefIdAndProfileRefIdAndEffectiveToDateTimeBefore(
          UUID.fromString(accountRefId), UUID.fromString(profileRefId), endTime, pageable);
    } else {
      balanceEntities = readBalanceRepository.findByAccountRefIdAndProfileRefId(
          UUID.fromString(accountRefId), UUID.fromString(profileRefId), pageable);
    }

    log.info("located {} snapshots", balanceEntities.size());

    if (balanceEntities.isEmpty()) {
      throw new ResourceNotFoundException(Messages.NO_BALANCES_FOUND);
    }

    return transactionMapper.fromBalanceEntityListToBalanceList(balanceEntities);
  }

  /**
   * {{@inheritDoc}}
   */
  @Override
  public List<Instruction> retrieveInstructionList(String profileRefId, String accountRefId, String interactionId)
      throws Exception {

    List<InstructionEntity> entities;
    try {

      Account account = validationService.getAccount(accountRefId, profileRefId, interactionId);
      // Account validation check
      if (account == null || !account.getRefId().equals(accountRefId)) {
        throw new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND);
      }

      // Profile validation check
      if (!account.getProfileId().equals(profileRefId)) {
        throw new ResourceNotFoundException(Messages.PROFILE_NOT_FOUND);
      }

      entities = readInstructionRepository.
          findTop25ByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(UUID.fromString(accountRefId),
              UUID.fromString(profileRefId));
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }

    return transactionMapper.fromInstructionEntityListToInstructionList(entities);
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public Transaction getTransactionByRefId(String transactionRefId)
      throws Exception {

    TransactionEntity ts;

    try {
      ts = readTransactionRepository.findByTransactionRefId(transactionRefId);

      // check if exists
      if (Objects.isNull(ts)) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }

    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
    return transactionMapper.fromTransactionEntityToTransaction(ts);
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public RetrieveTransactionsResponse searchTransaction(String profileRefId, String instructionRefId, String paymentRail,
      LocalDateTime startTime, LocalDateTime endTime, Integer offset, Integer maxResponseItems) throws Exception {
    RetrieveTransactionsResponse response = new RetrieveTransactionsResponse();
    try {
      // Validate mandatory input parameters
      if (Strings.isEmpty(profileRefId) || Strings.isEmpty(paymentRail)) {
        log.info("searchTransaction requires profileRefId and paymentRail as mandatory parameters");
        throw new ValidationException(Messages.TOO_FEW_QUERY_PARA, "too few search input parameters");
      }

      UUID profileUuid = UUID.fromString(profileRefId);

      log.debug("Search transactions with profileId:{}, instructionRefId:{}, paymentRail:{}, startTime:{}, endTime:{}, offset:{}, maxResponseItems:{}",
          profileRefId, instructionRefId, paymentRail, startTime, endTime, offset, maxResponseItems);

      // Calculate page number based on offset and maxResponseItems
      int pageNumber = offset / maxResponseItems;

      // Use Pageable to fetch transactions with pagination
      Pageable pageable = PageRequest.of(pageNumber, maxResponseItems + 1, Sort.by(Sort.Direction.DESC, "acceptanceDateTime"));

      // Perform the query to fetch transactions
      Slice<TransactionEntity> transactionSlice = findTransactionsWithInstructionSlice(
          profileUuid, instructionRefId, PaymentRailType.valueOf(paymentRail), startTime, endTime, pageable);

      List<TransactionEntity> transactionEntityList = transactionSlice.getContent();

      // Determine if there are more records beyond the maxResponseItems limit
      boolean moreRecords = transactionEntityList.size() > maxResponseItems;

      if (moreRecords) {
        transactionEntityList = transactionEntityList.subList(0, maxResponseItems);  // Trim to maxResponseItems
      }

      if (!transactionEntityList.isEmpty()) {
        List<LedgerRetrieveSearchTransaction> ledgerRetrieveTransactionList = transactionEntityList.stream()
            .map(this::mapToLedgerRetrieveTransaction)
            .collect(Collectors.toList());
        response.setTransactions(ledgerRetrieveTransactionList);
      }

      response.setMoreRecords(moreRecords || transactionSlice.hasNext());  // Indicate if there are more transaction records

    } catch (ValidationException validationException) {
      throw validationException;
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }

    return response;
  }

  private Slice<TransactionEntity> findTransactionsWithInstructionSlice(UUID profileRefId, String instructionRefId,
      PaymentRailType paymentRailType, LocalDateTime startTime,
      LocalDateTime endTime, Pageable pageable) {
    if (startTime != null && endTime != null) {
      if (Strings.isEmpty(instructionRefId)) {
        // Fetch transactions based on profileRefId, paymentRail, and date range
        return readTransactionRepository.findByProfileRefIdAndPaymentRailAndDateRange(
            profileRefId, paymentRailType, startTime, endTime, pageable);
      } else {
        // Fetch transactions based on profileRefId, instructionRefId, paymentRail, and date range
        return readTransactionRepository.findByProfileRefIdAndInstructionRefIdAndPaymentRailAndDateRange(
            profileRefId, instructionRefId, paymentRailType, startTime, endTime, pageable);
      }
    } else {
      if (Strings.isEmpty(instructionRefId)) {
        // Fetch transactions based on profileRefId and paymentRail
        return readTransactionRepository.findByProfileRefIdAndPaymentRail(
            profileRefId, paymentRailType, pageable);
      } else {
        // Fetch transactions based on profileRefId, instructionRefId, and paymentRail
        return readTransactionRepository.findByProfileRefIdAndInstructionRefIdAndPaymentRail(
            profileRefId, instructionRefId, paymentRailType, pageable);
      }
    }
  }

  private LedgerRetrieveSearchTransaction mapToLedgerRetrieveTransaction(TransactionEntity entity) {
    LedgerRetrieveSearchTransaction transaction = new LedgerRetrieveSearchTransaction();
    transaction.setTransactionRefId(entity.getTransactionRefId());
    transaction.setAmount(entity.getAmount());
    transaction.setAcceptanceDateTime(DateUtils.toOffsetDateTime(entity.getAcceptanceDateTime()));
    transaction.setInstructionRefId(entity.getInstruction().getInstructionRefId());
    transaction.setAccountRefId(entity.getInstruction().getAccountRefId());
    return transaction;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public RetrieveInstructionsResponse searchInstruction(String profileRefId, String accountRefId, String instructionRefId, String paymentRail,
      LocalDateTime startTime, LocalDateTime endTime, Integer offset, Integer maxResponseItems) throws Exception {
    RetrieveInstructionsResponse response = new RetrieveInstructionsResponse();
    try {
      UUID profileUuid = UUID.fromString(profileRefId);
      UUID accountUuid = UUID.fromString(accountRefId);

      log.debug("Search instructions with profileId:{}, accountId:{}, instructionRefId:{}, paymentRail:{}, startTime:{}, endTime:{}, offset:{}, maxResponseItems:{}",
          profileRefId, accountRefId, instructionRefId, paymentRail, startTime, endTime, offset, maxResponseItems);

      // Calculate page number based on offset and maxResponseItems
      int pageNumber = offset / maxResponseItems;

      // Use Pageable to fetch transactions with pagination
      Pageable pageable = PageRequest.of(pageNumber, maxResponseItems + 1, Sort.by(Sort.Direction.DESC, "createdDateTime"));

      // Perform the query to fetch transactions
      Slice<InstructionEntity> instructionSlice = findInstructionsWithProfileAndAccount(
          profileUuid, accountUuid, instructionRefId, paymentRail, startTime, endTime, pageable);

      if (instructionSlice == null || instructionSlice.isEmpty()) {
        log.error("instructions not found");
        throw new ResourceNotFoundException(Messages.INSTRUCTION_NOT_FOUND);
      }

      List<InstructionEntity> instructionEntityList = instructionSlice.getContent();

      // Determine if there are more records beyond the maxResponseItems limit
      boolean moreRecords = instructionEntityList.size() > maxResponseItems;

      if (moreRecords) {
        instructionEntityList = instructionEntityList.subList(0, maxResponseItems);  // Trim to maxResponseItems
      }

      if (!instructionEntityList.isEmpty()) {
        List<LedgerRetrieveSearchInstruction> ledgerRetrieveSearchInstructionList = instructionEntityList.stream()
            .map(this::mapToLedgerRetrieveInstruction)
            .collect(Collectors.toList());
        response.setInstructions(ledgerRetrieveSearchInstructionList);
      }

      response.setMoreRecords(moreRecords || instructionSlice.hasNext());  // Indicate if there are more instructions records

    } catch (ValidationException validationException) {
      throw validationException;
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }

    return response;
  }

  @Override
  public boolean updateTransactionMetadata(String instructionRefId, String transactionRefId, String profileRefId,
      String accountRefId, String interactionId, Metadata metaData) throws Exception {
    AtomicBoolean isNewEntity = new AtomicBoolean(false);
    try {
      //validation the profileId and accountId
      validationService.validateProfileAndAccount(accountRefId, profileRefId, interactionId);

      /*
       * find the existing transaction
       */
      List<TransactionEntity> ts = transactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(instructionRefId,
          transactionRefId, UUID.fromString(profileRefId), UUID.fromString(accountRefId));

      // check if exists
      if (Objects.isNull(ts) || ts.isEmpty()) {
        throw new ResourceNotFoundException(Messages.TRANSACTION_NOT_FOUND);
      }

      //since we use transaction ref id found it we should only have 1
      TransactionEntity transaction = ts.get(0);

      //we only allow internal transaction to add metaData
      if (transaction.getInstruction().getPaymentRail() != PaymentRailType.INTERNAL) {
        throw new InvalidFieldException(Messages.INVALID_PAYMENT_RAIL);
      }

      // Check if transaction metadata exists
      TransactionMetadataEntity transactionMetadataEntity = transactionMetadataRepository
          .findByTransactionEntity(transaction)
          .orElseGet(() -> {
            isNewEntity.set(true);
            log.debug("AfterSettingToTrue:{}", isNewEntity);
            return new TransactionMetadataEntity();
          });
      transactionMetadataEntity.setTransactionEntity(transaction);

      // Serialize MetadataJsonDto into JSON string
      ObjectMapper objectMapper = new ObjectMapper();
      String metadataJsonString = objectMapper.writeValueAsString(metaData);

      // Deserialize the JSON string into a Map
      Map<String, Object> metadataJsonMap = objectMapper.readValue(metadataJsonString, Map.class);

      // Set the Map to metaDataJson field
      transactionMetadataEntity.setMetaDataJson(metadataJsonMap);

      transactionMetadataRepository.save(transactionMetadataEntity);

    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (InvalidFieldException ex) {
      throw new InvalidFieldException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
    return isNewEntity.get();
  }

  @Override
  public RetrieveMetadata getTransactionMetadata(String instructionRefId, String transactionRefId, String profileRefId,
      String accountRefId, String interactionId) throws Exception {
    try {
      //validation the information
      validationService.validateProfileAndAccount(accountRefId, profileRefId, interactionId);

      //find the existing transaction and update notes
      List<TransactionEntity> ts = transactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(instructionRefId,
          transactionRefId, UUID.fromString(profileRefId), UUID.fromString(accountRefId));

      // check if exists
      if (Objects.isNull(ts) || ts.isEmpty()) {
        throw new ResourceNotFoundException(Messages.INVALID_INPUT);
      }
      TransactionEntity transactionEntity = ts.get(0);

      Optional<TransactionMetadataEntity> transactionMetadataEntityOpt = this.transactionMetadataRepository.findByTransactionEntity(transactionEntity);

      if (transactionMetadataEntityOpt.isEmpty()) {
        throw new ResourceNotFoundException(Messages.TRANSACTION_METADATA_NOT_FOUND);
      }

      RetrieveMetadata retrieveMetadata = new RetrieveMetadata();
      TransactionMetadataEntity transactionMetadataEntity = transactionMetadataEntityOpt.get();

      // Extracting values from metaDataJson
      Map<String, Object> metaDataJson = transactionMetadataEntity.getMetaDataJson();

      if (metaDataJson != null) {
        // Extract and set ticketId
        if (metaDataJson.containsKey("ticket_id")) {
          if (metaDataJson.get("ticket_id") != null) {
            retrieveMetadata.setTicketId((String) metaDataJson.get("ticket_id"));
          }
        }

        // Extract and set ticketType (ensure the value is properly mapped to the enum)
        if (metaDataJson.containsKey("ticket_type")) {
          if (metaDataJson.get("ticket_type") != null) {
            String ticketType = (String) metaDataJson.get("ticket_type");
            retrieveMetadata.setTicketType(RetrieveMetadata.TicketTypeEnum.fromValue(ticketType));
          }
        }

        // Extract and set notes
        if (metaDataJson.containsKey("notes")) {
          if (metaDataJson.get("notes") != null) {
            retrieveMetadata.setNotes((String) metaDataJson.get("notes"));
          }
        }
      }

      retrieveMetadata.setCreatedDateTime(DateUtils.toOffsetDateTime(transactionMetadataEntity.getCreatedDateTime()));
      retrieveMetadata.setUpdatedDateTime(DateUtils.toOffsetDateTime(transactionMetadataEntity.getUpdatedDateTime()));

      return retrieveMetadata;

    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getMessage());
    } catch (NullPointerException ex) {
      throw new NullPointerException(ex.getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
  }

  private Slice<InstructionEntity> findInstructionsWithProfileAndAccount(
      UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      String paymentRail,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Pageable pageable
  ) {
    log.info(
        "Search instructions with profileId:{}, accountId:{}, instructionRefId:{}, paymentRail:{}, startTime:{}, endTime:{}",
        profileRefId, accountRefId, instructionRefId, paymentRail, startTime, endTime
    );

    // -----------------------------------------
    // CASE 1: Both startTime and endTime exist
    // -----------------------------------------
    if (startTime != null && endTime != null) {
      if (Strings.isEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndCreatedDateTimeBetween(
            profileRefId, accountRefId, startTime, endTime, pageable
        );
      } else if (Strings.isNotEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndCreatedDateTimeBetween(
            profileRefId, accountRefId, instructionRefId, startTime, endTime, pageable
        );
      } else if (Strings.isEmpty(instructionRefId) && Strings.isNotEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndPaymentRailAndCreatedDateTimeBetween(
            profileRefId, accountRefId, PaymentRailType.valueOf(paymentRail),
            startTime, endTime, pageable
        );
      } else {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRailAndCreatedDateTimeBetween(
            profileRefId, accountRefId, instructionRefId,
            PaymentRailType.valueOf(paymentRail), startTime, endTime, pageable
        );
      }
    }

    // --------------------------------------------------
    // CASE 2: startTime != null, endTime == null
    // --------------------------------------------------
    else if (startTime != null && endTime == null) {
      if (Strings.isEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndCreatedDateTimeAfter(
            profileRefId, accountRefId, startTime, pageable
        );
      } else if (Strings.isNotEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndCreatedDateTimeAfter(
            profileRefId, accountRefId, instructionRefId, startTime, pageable
        );
      } else if (Strings.isEmpty(instructionRefId) && Strings.isNotEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndPaymentRailAndCreatedDateTimeAfter(
            profileRefId, accountRefId, PaymentRailType.valueOf(paymentRail),
            startTime, pageable
        );
      } else {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRailAndCreatedDateTimeAfter(
            profileRefId, accountRefId, instructionRefId,
            PaymentRailType.valueOf(paymentRail), startTime, pageable
        );
      }
    }

    // --------------------------------------------------
    // CASE 3: startTime == null, endTime != null
    // --------------------------------------------------
    else if (startTime == null && endTime != null) {
      if (Strings.isEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndCreatedDateTimeBefore(
            profileRefId, accountRefId, endTime, pageable
        );
      } else if (Strings.isNotEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndCreatedDateTimeBefore(
            profileRefId, accountRefId, instructionRefId, endTime, pageable
        );
      } else if (Strings.isEmpty(instructionRefId) && Strings.isNotEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndPaymentRailAndCreatedDateTimeBefore(
            profileRefId, accountRefId, PaymentRailType.valueOf(paymentRail),
            endTime, pageable
        );
      } else {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRailAndCreatedDateTimeBefore(
            profileRefId, accountRefId, instructionRefId,
            PaymentRailType.valueOf(paymentRail), endTime, pageable
        );
      }
    }

    // --------------------------------------------------
    // CASE 4: startTime == null and endTime == null
    // --------------------------------------------------
    else {
      log.info("startTime and endTime null");
      if (Strings.isEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefId(
            profileRefId, accountRefId, pageable
        );
      } else if (Strings.isNotEmpty(instructionRefId) && Strings.isEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
            profileRefId, accountRefId, instructionRefId, pageable
        );
      } else if (Strings.isEmpty(instructionRefId) && Strings.isNotEmpty(paymentRail)) {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndPaymentRail(
            profileRefId, accountRefId, PaymentRailType.valueOf(paymentRail), pageable
        );
      } else {
        return readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRail(
            profileRefId, accountRefId, instructionRefId,
            PaymentRailType.valueOf(paymentRail), pageable
        );
      }
    }
  }

  private LedgerRetrieveSearchInstruction mapToLedgerRetrieveInstruction(InstructionEntity entity) {
    LedgerRetrieveSearchInstruction instruction = new LedgerRetrieveSearchInstruction();
    instruction.setInstructionRefId(entity.getInstructionRefId());
    instruction.setCreatedDateTime(DateUtils.toOffsetDateTime(entity.getCreatedDateTime()));
    instruction.setUpdatedDateTime(DateUtils.toOffsetDateTime(entity.getUpdatedDateTime()));
    instruction.setPaymentRail(PaymentRail.fromValue(entity.getPaymentRail().name()));
    instruction.setStatus(mapStatus(entity.getStatus()));
    return instruction;
  }

  private com.peoplestrust.transaction.domain.model.InstructionStatus mapStatus(InstructionStatus internalStatus) {
    // Mapping logic
    return switch (internalStatus) {
      case INIT_PENDING, PENDING -> com.peoplestrust.transaction.domain.model.InstructionStatus.PENDING;
      case FAILED -> com.peoplestrust.transaction.domain.model.InstructionStatus.FAILED;
      case ROLLBACKED, ROLLBACKED_SYSTEM -> com.peoplestrust.transaction.domain.model.InstructionStatus.ROLLBACKED;
      case POSTED -> com.peoplestrust.transaction.domain.model.InstructionStatus.POSTED;
      default -> throw new IllegalArgumentException("Unknown internal status: " + internalStatus);
    };
  }
}