# S3 Event-Driven EKS Job Trigger Lambda

This Lambda function is designed to be triggered by S3 events (e.g., when a file is dropped in a specific S3 bucket/folder). When triggered, it creates a **Kubernetes Job** in EKS. The Job spec is derived from an existing CronJob template for convenience, enabling event-driven, on-demand execution of workloads in EKS.

## Key Features

- **Dynamic S3 Processing**: Automatically extracts S3 bucket and key information from the triggering event
- **Argument Override**: Replaces hardcoded `--s3-bucket` and `--s3-key` arguments in the CronJob template with actual values from the S3 event
- **Event-Driven Execution**: Processes the exact file that triggered the Lambda, not a hardcoded file

## Usage

1. **Log into ECR**
aws ecr get-login-password --region ca-central-1 --profile=NON_LEDGER | docker login --username AWS --password-stdin 799455639446.dkr.ecr.ca-central-1.amazonaws.com

2. **Build and deploy the Lambda container (automated)**
```bash
cd eks-lambda
./deploy.sh
```

**Or manually:**
```bash
# Clean the local docker file
docker system prune -af --volumes
# Build docker image and push to ECR
docker build --platform linux/amd64 -t eks-lambda .
docker image ls | awk '$1 == "eks-lambda" && $2 == "latest" {print $3}'
docker tag <image-id> 799455639446.dkr.ecr.ca-central-1.amazonaws.com/tol-lambda-eks-qa:<timestamp>
docker push 799455639446.dkr.ecr.ca-central-1.amazonaws.com/tol-lambda-eks-qa:<timestamp>
# Update Lambda function
aws lambda update-function-code --function-name tol-lambda-eks-qa --image-uri 799455639446.dkr.ecr.ca-central-1.amazonaws.com/tol-lambda-eks-qa:<timestamp>
```

3. **Update kubectl configMap**
kubectl edit configmap aws-auth -n kube-system

4. **Check EKS permission for RBAC**
kubectl auth can-i create jobs --namespace pg-ledger-qa --as lambda-job-creator
kubectl auth can-i get cronjobs --namespace pg-ledger-qa --as lambda-job-creator

5. **Test S3 event processing logic locally (optional)**
python3 test_s3_event.py

Note: This tests the S3 event parsing logic without requiring Kubernetes dependencies.

6. **Check job result**
kubectl get pods -n pg-ledger-qa -l job-name=<job-name> -o name
kubectl logs -n pg-ledger-qa $(kubectl get pods -n pg-ledger-qa -l job-name=<job-name> -o name)

## Dependencies

The Lambda function requires these Python packages (included in the container):
- `kubernetes` - For EKS API interactions
- `boto3` - For AWS services (included in Lambda runtime)

## Notes
- The Lambda does **not** trigger a CronJob schedule. It creates a one-off Job using the CronJob as a template.
- This enables S3 event-driven, on-demand execution of EKS Jobs.
- **S3 Event Processing**: The Lambda automatically extracts bucket and key information from S3 events and overrides the CronJob template arguments.
- **Placeholder Arguments**: CronJob templates now use `PLACEHOLDER_BUCKET` and `PLACEHOLDER_KEY` which are replaced with actual S3 event data.
- **Local Testing**: Use `test_s3_event.py` to validate S3 event parsing logic without needing Kubernetes dependencies.
- For more details, refer to the PlantUML diagram and the Lambda code.

## Reference
https://peoplesgroup.atlassian.net/wiki/spaces/ALZC/pages/4181950621/Lambda+Management#Accessing-EKS-from-Lambda