package com.peoplestrust.schedulers.service;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.entity.MonetaryUnit;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.schedulers.config.SchedulersApiConstant;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.mapper.CommonMapper;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BalanceSnapshotServiceImpl implements BalanceSnapshotService {

  @Autowired
  ReadTransactionRepository readTransactionRepository;

  @Autowired
  ReadAccountRepository readAccountRepository;

  @Autowired
  BalanceRepository balanceRepository;

  @Autowired
  ReadBalanceRepository readBalanceRepository;

  @Override
  public List<BalanceEntity> generateTransactionReBalanceSnapshot() {
    List<BalanceEntity> balanceEntities = new ArrayList<>();
    try {
      balanceEntities = generateBalanceEntities();
    } catch (JpaSystemException e) {
      log.error(SchedulersApiConstant.DATABASE_ERROR + e.getMessage(), e);
    } catch (Exception e) {
      log.error(SchedulersApiConstant.GENERIC_ERROR + e.getMessage(), e);
    }
    return balanceEntities;
  }

  private List<BalanceEntity> generateBalanceEntities() {
    List<BalanceEntity> balanceEntities = new ArrayList<>();
    LocalDateTime now = LocalDateTime.now();
    ZonedDateTime zonedDateTimeET = now.atZone(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO));
    OffsetDateTime effectiveToDateTimeUTC = CommonMapper.offSetToOffSet(zonedDateTimeET.minusHours(1).toOffsetDateTime());

    log.info("Starting snapshot generation for effective date up to {}", effectiveToDateTimeUTC);

    List<AccountEntity> accounts = readAccountRepository.findAll();
    if (accounts.isEmpty()) {
      log.info("No accounts found to process for snapshot generation.");
      return balanceEntities;
    }

    accounts.forEach(account -> {
      Optional<BalanceEntity> previousSnapshotOpt = readBalanceRepository
          .findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(account.getRefId(), account.getProfileId());

      if (previousSnapshotOpt.isPresent()) {
        processAccountWithPreviousSnapshot(account, previousSnapshotOpt.get(), effectiveToDateTimeUTC.toLocalDateTime(), balanceEntities);
      } else {
        processAccountWithoutPreviousSnapshot(account, effectiveToDateTimeUTC.toLocalDateTime(), balanceEntities);
      }
    });

    return balanceEntities;
  }

  private void processAccountWithPreviousSnapshot(AccountEntity account, BalanceEntity previousSnapshot,
      LocalDateTime effectiveToDateTime, List<BalanceEntity> balanceEntities) {

    LocalDateTime effectiveFromDateTime = previousSnapshot.getEffectiveToDateTime();

    if (effectiveToDateTime.isBefore(effectiveFromDateTime) || effectiveToDateTime.isEqual(effectiveFromDateTime)) {
      log.info("Skipping snapshot for account {}, as a current one already exists for time {}", account.getRefId(), effectiveToDateTime);
      return;
    }

    logEffectiveDateTimes(account, effectiveFromDateTime, effectiveToDateTime);

    TransactionAmounts amounts = fetchTransactionAmounts(account, effectiveFromDateTime, effectiveToDateTime);

    // Previous total + newly posted transactions (including those that were pending before)
    BigDecimal newTotalAmount = previousSnapshot.getTotalAmount().add(amounts.getFinalizedAmountDelta());

    // This is the sum of all transactions currently in PENDING status
    BigDecimal newTotalPendingAmount = amounts.getTotalPendingAmount();

    // Reserve calculation remains a delta
    BigDecimal newTotalReserve = previousSnapshot.getTotalReserveAmount().add(amounts.getReserveTotalDelta());

    BalanceEntity newBalanceEntity = createBalanceEntity(account, effectiveFromDateTime, effectiveToDateTime,
        newTotalAmount, newTotalReserve, newTotalPendingAmount);

    balanceEntities.add(balanceRepository.save(newBalanceEntity));
    log.info("Generated new balance snapshot for account {}: {}", account.getRefId(), newBalanceEntity);
  }

  private void processAccountWithoutPreviousSnapshot(AccountEntity account,
      LocalDateTime effectiveToDateTime, List<BalanceEntity> balanceEntities) {

    // For the first snapshot, the start date is arbitrary but should be far in the past to capture all history.
    LocalDateTime effectiveFromDateTime = LocalDateTime.of(2000, 1, 1, 0, 0);
    logEffectiveDateTimes(account, effectiveFromDateTime, effectiveToDateTime);

    TransactionAmounts amounts = fetchTransactionAmounts(account, effectiveFromDateTime, effectiveToDateTime);

    // For the first snapshot, the total amount *is* the delta.
    BigDecimal totalAmount = amounts.getFinalizedAmountDelta();
    BigDecimal totalPendingAmount = amounts.getTotalPendingAmount();
    BigDecimal totalReserve = amounts.getReserveTotalDelta();

    BalanceEntity newBalanceEntity = createBalanceEntity(account, effectiveFromDateTime, effectiveToDateTime,
        totalAmount, totalReserve, totalPendingAmount);

    balanceEntities.add(balanceRepository.save(newBalanceEntity));
    log.info("Generated FIRST balance snapshot for account {}: {}", account.getRefId(), newBalanceEntity);
  }

  private TransactionAmounts fetchTransactionAmounts(AccountEntity account, LocalDateTime periodStart, LocalDateTime periodEnd) {
    // Get the SUM of amounts for transactions FINALIZED in this period.
    Optional<BigDecimal> finalizedAmountDeltaOpt = readTransactionRepository.sumFinalizedAmountForSnapshotPeriod(
        account.getRefId(), account.getProfileId(), periodStart, periodEnd);

    // Get the ABSOLUTE total of all PENDING transactions as of the snapshot time (periodEnd)
    Optional<BigDecimal> totalPendingAmountOpt = readTransactionRepository.getTotalPendingAmountForAccountAsOf(
        account.getRefId(), account.getProfileId(), periodEnd);

    // Reserve amount delta (this logic seems correct as a delta).
    Optional<BigDecimal> reserveTotalDeltaOpt = readTransactionRepository.getReserveAmountBS(
        account.getRefId(), account.getProfileId(), periodStart, periodEnd);

    BigDecimal finalizedAmountDelta = finalizedAmountDeltaOpt.orElse(BigDecimal.ZERO);
    BigDecimal totalPendingAmount = totalPendingAmountOpt.orElse(BigDecimal.ZERO);
    BigDecimal reserveTotalDelta = reserveTotalDeltaOpt.orElse(BigDecimal.ZERO);

    log.info("For account {}, period [{} to {}]: Finalized Delta = {}, Reserve Delta = {}",
        account.getRefId(), periodStart, periodEnd, finalizedAmountDelta, reserveTotalDelta);
    log.info("For account {}, Total Pending Amount AS OF {} = {}", account.getRefId(), periodEnd, totalPendingAmount);

    return TransactionAmounts.builder()
        .finalizedAmountDelta(finalizedAmountDelta)
        .totalPendingAmount(totalPendingAmount)
        .reserveTotalDelta(reserveTotalDelta)
        .build();
  }

  private BalanceEntity createBalanceEntity(AccountEntity account, LocalDateTime from, LocalDateTime to,
      BigDecimal totalAmount, BigDecimal totalReserve, BigDecimal pendingTotal) {

    BalanceEntity balanceEntity = new BalanceEntity();
    balanceEntity.setMonetaryUnit(MonetaryUnit.valueOf(String.valueOf(account.getMonetaryUnit())));
    balanceEntity.setProfileRefId(account.getProfileId());
    balanceEntity.setAccountRefId(account.getRefId());
    balanceEntity.setEffectiveFromDateTime(from);
    balanceEntity.setEffectiveToDateTime(to);
    balanceEntity.setTotalAmount(totalAmount);
    balanceEntity.setTotalPendingAmount(pendingTotal);
    balanceEntity.setTotalReserveAmount(totalReserve);
    balanceEntity.setTotalAmountCredit(BigDecimal.ZERO);
    balanceEntity.setTotalAmountDebit(BigDecimal.ZERO);
    return balanceEntity;
  }

  private void logEffectiveDateTimes(AccountEntity account, LocalDateTime from, LocalDateTime to) {
    log.info("Processing snapshot for account {} for period [{} to {}]", account.getRefId(), from, to);
  }

  @Data
  @Builder
  private static class TransactionAmounts {
    private BigDecimal finalizedAmountDelta;
    private BigDecimal totalPendingAmount;
    private BigDecimal reserveTotalDelta;
  }
}