package com.peoplestrust.transaction.persistence.repository.read;

import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ReadTransactionRepository extends JpaRepository<TransactionEntity, Integer> {

  /**
   * ` Retrieve transaction by profile, account, instruction and transaction reference IDs.
   *
   * @param instructionRefId instruction reference ID
   * @param transactionRefId transaction reference ID
   * @param profileRefId     profile reference ID
   * @param accountRefId     account reference ID
   * @return list of transactions
   */
  @Query(value = "FROM TransactionEntity t where "
      + " instruction.instructionRefId =:instructionRefId and transactionRefId =:transactionRefId and "
      + " t.profileRefId =:profileRefId and t.accountRefId =:accountRefId ")
  List<TransactionEntity> findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(
      @Param("instructionRefId") String instructionRefId,
      @Param("transactionRefId") String transactionRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("accountRefId") UUID accountRefId);

  /**
   * (Internal ONLY) Retrieves transaction by its reference ID.
   *
   * @param transactionRefId transaction reference ID
   * @return
   */
  TransactionEntity findByTransactionRefId(String transactionRefId);

  /**
   * Return sum of all CREDITS and DEBITS within a date range and up to the current instruction.
   *
   * @param accountRefId           account reference ID
   * @param profileRefId           profile reference ID
   * @param effectiveDateTimeStart date and time to start (inclusive) searching from
   * @param effectiveDateTimeUntil date and time to search until (inclusive)
   * @param instructionId
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + " (accountRefId=:accountRefId and profileRefId=:profileRefId) "
      + " and status in ('POSTED','PENDING','INIT_PENDING','REVERSED') "
      + " and (effectiveDateTime >=:effectiveDateTimeStart and effectiveDateTime <=:effectiveDateTimeUntil) "
      + " and instruction.id <=:instructionId ")
  Optional<BigDecimal> sumAmountByDateRangeInstructionId(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeUntil") LocalDateTime effectiveDateTimeUntil,
      @Param("instructionId") Integer instructionId);

  @Query(nativeQuery = true, value = "SELECT sumAmountByDateRangeInstructionId(:accountRefId, :profileRefId, :effectiveDateTimeStart, :effectiveDateTimeUntil, :instructionId)")
  Optional<BigDecimal> sumAmountByDateRangeInstructionId_SP(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeUntil") LocalDateTime effectiveDateTimeUntil,
      @Param("instructionId") Integer instructionId);

  /**
   * Return sum of all CREDITS and DEBITS within a date range.
   *
   * @param accountRefId           account reference ID
   * @param profileRefId           profile reference ID
   * @param effectiveDateTimeStart date and time to start (inclusive) searching from
   * @param effectiveDateTimeUntil date and time to search until (inclusive)
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + "(accountRefId=:accountRefId and profileRefId=:profileRefId) "
      + "and status in ('POSTED','PENDING','INIT_PENDING','REVERSED') "
      + "and (effectiveDateTime >=:effectiveDateTimeStart and effectiveDateTime <=:effectiveDateTimeUntil) ")
  Optional<BigDecimal> sumAmountByDateRange(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeUntil") LocalDateTime effectiveDateTimeUntil);

  /**
   * Return sum of all RESERVE CREDITS or RESERVE DEBITS within a date range.
   *
   * @param accountId              account reference ID
   * @param profileId              profile reference ID
   * @param effectiveDateTimeStart date and time to start (inclusive) searching from
   * @param effectiveDateTime      current date and time
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + "accountRefId=:accountRefId and profileRefId=:profileRefId "
      + "and instruction.paymentRail ='INTERNAL' and  paymentCategory ='RESERVE' and status in ('POSTED') "
      + "and effectiveDateTime >=:effectiveDateTimeStart and effectiveDateTime <=:effectiveDateTime ")
  Optional<BigDecimal> getReserveAmount(
      @Param("accountRefId") UUID accountId,
      @Param("profileRefId") UUID profileId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTime") LocalDateTime effectiveDateTime);

  /**
   * Return sum of all funds on hold from current date and time.
   *
   * @param accountId         account reference ID
   * @param profileId         profile reference ID
   * @param transactionHold   HOLD
   * @param effectiveDateTime date and time to start (exclusive) searching from
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + "accountRefId=:accountRefId and profileRefId=:profileRefId "
      + "and status in ('PENDING','POSTED','REVERSED') "
      + "and transactionHold=:transactionHold "
      + "and effectiveDateTime >:effectiveDateTime ")
  Optional<BigDecimal> getFundHoldAmount(
      @Param("accountRefId") UUID accountId,
      @Param("profileRefId") UUID profileId,
      @Param("transactionHold") TransactionHoldType transactionHold,
      @Param("effectiveDateTime") LocalDateTime effectiveDateTime);

  /**
   * Return sum of all CREDITS and DEBITS within a date range for balance snapshot.
   *
   * @param accountRefId           account reference ID
   * @param profileRefId           profile reference ID
   * @param effectiveDateTimeStart date and time to start (inclusive) searching from
   * @param effectiveDateTimeEnd   date and time to search until (exclusive)
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + "accountRefId=:accountRefId and profileRefId=:profileRefId "
      + "and status in ('POSTED','REVERSED') "
      + "and effectiveDateTime >=:effectiveDateTimeStart and effectiveDateTime <:effectiveDateTimeEnd ")
  Optional<BigDecimal> sumAmountByDateRangeBS(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeEnd") LocalDateTime effectiveDateTimeEnd);

  /**
   * Return sum of all RESERVE CREDITS or DEBITS within a date range for balance snapshot.
   *
   * @param accountId              account reference ID
   * @param profileId              profile reference ID
   * @param effectiveDateTimeStart date and time to start (inclusive) searching from
   * @param effectiveDateTimeEnd   date and time to search until (exclusive)
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + "accountRefId=:accountRefId and profileRefId=:profileRefId "
      + "and status in ('POSTED') and instruction.paymentRail ='INTERNAL' and  paymentCategory ='RESERVE' "
      + "and effectiveDateTime >=:effectiveDateTimeStart and effectiveDateTime <:effectiveDateTimeEnd ")
  Optional<BigDecimal> getReserveAmountBS(
      @Param("accountRefId") UUID accountId,
      @Param("profileRefId") UUID profileId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeEnd") LocalDateTime effectiveDateTimeEnd);


  /**
   * Return sum of all funds on hold within a date range for balance snapshot.
   *
   * @param accountId              account reference ID
   * @param profileId              profile reference ID
   * @param transactionHold        HOLD
   * @param effectiveDateTimeStart date and time to start (inclusive) searching from
   * @param effectiveDateTimeEnd   date and time to search until (exclusive)
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + "accountRefId=:accountRefId and profileRefId=:profileRefId "
      + "and status in ('POSTED','REVERSED') "
      + "and transactionHold=:transactionHold "
      + "and effectiveDateTime >=:effectiveDateTimeStart and effectiveDateTime <:effectiveDateTimeEnd ")
  Optional<BigDecimal> getFundHoldAmount(
      @Param("accountRefId") UUID accountId,
      @Param("profileRefId") UUID profileId,
      @Param("transactionHold") TransactionHoldType transactionHold,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeEnd") LocalDateTime effectiveDateTimeEnd);

  /**
   * Sum of PENDING + INIT_PENDING for the snapshot job.
   *
   * @param accountRefId              account reference ID
   * @param profileRefId              profile reference ID
   * @param effectiveDateTimeStart    date and time to start (inclusive) searching from
   * @param effectiveDateTimeEnd      date and time to search until (exclusive)
   * @return
   */
  @Query(value = "select coalesce(sum(t.amount), 0) FROM TransactionEntity t where "
      + "t.accountRefId=:accountRefId and t.profileRefId=:profileRefId "
      + "and t.status in ('PENDING','INIT_PENDING') "
      + "and t.effectiveDateTime >=:effectiveDateTimeStart and t.effectiveDateTime <:effectiveDateTimeEnd")
  Optional<BigDecimal> getPendingAmountBS(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeEnd") LocalDateTime effectiveDateTimeEnd);

  // NEW method – returns the latest autorollback (if any) for the account/profile
  @Query("""
       SELECT MAX(t.effectiveDateTime)
       FROM TransactionEntity t
       WHERE t.accountRefId  = :accountRefId
         AND t.profileRefId  = :profileRefId
         AND t.status        = 'ROLLBACKED_SYSTEM'
     """)
  Optional<LocalDateTime> findLastAutoRollbackDate(UUID accountRefId, UUID profileRefId);

  @Query("SELECT t FROM TransactionEntity t WHERE t.profileRefId = :profileRefId AND t.instruction.instructionRefId = :instructionRefId AND t.instruction.paymentRail = :paymentRailType AND t.acceptanceDateTime BETWEEN :startTime AND :endTime ORDER BY t.acceptanceDateTime DESC")
  Slice<TransactionEntity> findByProfileRefIdAndInstructionRefIdAndPaymentRailAndDateRange(
      @Param("profileRefId") UUID profileRefId,
      @Param("instructionRefId") String instructionRefId,
      @Param("paymentRailType") PaymentRailType paymentRailType,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime,
      Pageable pageable);

  @Query("SELECT t FROM TransactionEntity t WHERE t.profileRefId = :profileRefId AND t.instruction.paymentRail = :paymentRailType AND t.acceptanceDateTime BETWEEN :startTime AND :endTime ORDER BY t.acceptanceDateTime DESC")
  Slice<TransactionEntity> findByProfileRefIdAndPaymentRailAndDateRange(
      @Param("profileRefId") UUID profileRefId,
      @Param("paymentRailType") PaymentRailType paymentRailType,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime,
      Pageable pageable);

  @Query("SELECT t FROM TransactionEntity t WHERE t.profileRefId = :profileRefId AND t.instruction.instructionRefId = :instructionRefId AND t.instruction.paymentRail = :paymentRailType ORDER BY t.acceptanceDateTime DESC")
  Slice<TransactionEntity> findByProfileRefIdAndInstructionRefIdAndPaymentRail(
      @Param("profileRefId") UUID profileRefId,
      @Param("instructionRefId") String instructionRefId,
      @Param("paymentRailType") PaymentRailType paymentRailType,
      Pageable pageable);

  @Query("SELECT t FROM TransactionEntity t WHERE t.profileRefId = :profileRefId AND t.instruction.paymentRail = :paymentRailType ORDER BY t.acceptanceDateTime DESC")
  Slice<TransactionEntity> findByProfileRefIdAndPaymentRail(
      @Param("profileRefId") UUID profileRefId,
      @Param("paymentRailType") PaymentRailType paymentRailType,
      Pageable pageable);

  /**
   * Return every transactionRefId that already exists for this profile + account and is in the incoming list.
   */
  @Query("""
      select t.transactionRefId
      from   TransactionEntity t
      where  t.profileRefId   = :profile
        and  t.accountRefId   = :account
        and  t.transactionRefId in :refs
      """)
  List<String> findExistingTransactionRefs(@Param("profile") UUID profile,
      @Param("account") UUID account,
      @Param("refs") List<String> refs);

  /**
   * QUERY 1: Calculates the delta of finalized transaction amounts within a given period.
   * This query finds the sum of amounts for transactions that reached a final state (POSTED, REVERSED, etc.)
   * within the snapshot's time window. This is a CHANGE/DELTA value.
   */
  @Query("""
        SELECT COALESCE(SUM(t.amount), 0)
        FROM TransactionEntity t
        WHERE t.accountRefId = :accountRefId
          AND t.profileRefId = :profileRefId
          AND t.status IN ('POSTED', 'REVERSED')
          AND t.finalizationDateTime >= :periodStart
          AND t.finalizationDateTime < :periodEnd
        """)
  Optional<BigDecimal> sumFinalizedAmountForSnapshotPeriod(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("periodStart") LocalDateTime periodStart,
      @Param("periodEnd") LocalDateTime periodEnd);

  /**
   * QUERY 2: Calculates the ABSOLUTE total pending amount for an account.
   * This query finds the sum of all transactions for an account that are currently in 'PENDING' status,
   * regardless of when they were created. This is an ABSOLUTE value.
   */
  @Query("""
    SELECT COALESCE(SUM(t.amount), 0) 
    FROM TransactionEntity t 
    WHERE t.accountRefId = :accountRefId 
      AND t.profileRefId = :profileRefId 
      AND t.createdDateTime <= :asOfDateTime 
      AND (t.finalizationDateTime IS NULL OR t.finalizationDateTime > :asOfDateTime)
      AND t.status <> 'FAILED'
    """)
 Optional<BigDecimal> getTotalPendingAmountForAccountAsOf(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("asOfDateTime") LocalDateTime asOfDateTime);

}
