package com.peoplestrust.transaction.api.v1.service;

import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.transaction.api.v1.config.TransactionProperty;
import com.peoplestrust.transaction.api.v1.exception.DuplicateTransactionException;
import com.peoplestrust.transaction.api.v1.mapper.TransactionMapper;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.persistence.entity.*;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.exception.InactiveStatusException;
import com.peoplestrust.util.api.common.util.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("TransactionServiceImpl Tests")
class InitiateInstructionServiceNewTest {

  @Mock
  private TransactionRepository transactionRepository;
  @Mock
  private InstructionRepository instructionRepository;
  @Mock
  private ReadBalanceRepository readBalanceRepository;
  @Mock
  private ValidationService validationService;
  @Mock
  private TransactionMapper transactionMapper;
  @Mock
  private TransactionProperty transactionProperty;

  @InjectMocks
  private TransactionServiceImpl transactionService;

  @Captor
  private ArgumentCaptor<InstructionEntity> instructionEntityCaptor;
  @Captor
  private ArgumentCaptor<List<TransactionEntity>> transactionEntityListCaptor;

  private String accountId;
  private String profileId;
  private String interactionId;
  private Account activeAccount;

  @BeforeEach
  void setUp() {
    accountId = UUID.randomUUID().toString();
    profileId = UUID.randomUUID().toString();
    interactionId = UUID.randomUUID().toString();
    activeAccount = createActiveAccount(accountId, profileId);
  }

  @Nested
  @DisplayName("initiateInstruction Tests")
  class InitiateInstructionTests {

    @Test
    @DisplayName("Should succeed with PENDING status when balance is sufficient")
    void initiateInstruction_shouldSucceed_whenBalanceIsSufficient() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithDebit("-50");
      InstructionEntity instructionEntity = createInstructionEntity(instruction);
      List<TransactionEntity> transactionEntities = createTransactionEntities(instruction);
      setupCommonMocks(instruction, instructionEntity, transactionEntities);

      // Mocks for balance calculation - NEW LOGIC
      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(false);
      when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any()))
          .thenReturn(Optional.of(createBalanceEntity(new BigDecimal("1000"), new BigDecimal("200")))); // Total: 1000, Pending: 200
      when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("-50")));
      when(transactionRepository.sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(BigDecimal.ZERO));
      // Effective balance = 1000 + 200 + (-50 - 0) = 1150. Sufficient.

      // Act
      Instruction result = transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);

      // Assert
      verify(instructionRepository, times(2)).save(instructionEntityCaptor.capture());
      InstructionEntity finalSavedEntity = instructionEntityCaptor.getValue();

      assertNotNull(result);
      assertEquals(InstructionStatus.PENDING, finalSavedEntity.getStatus());
      finalSavedEntity.getTransactions().forEach(t -> assertEquals(TransactionStatus.PENDING, t.getStatus()));
    }

    @Test
    @DisplayName("Should succeed without balance check for credit-only instructions")
    void initiateInstruction_shouldSucceed_withoutBalanceCheck_forCreditOnly() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithCreditOnly();
      InstructionEntity instructionEntity = createInstructionEntity(instruction);
      List<TransactionEntity> transactionEntities = createTransactionEntities(instruction);
      setupCommonMocks(instruction, instructionEntity, transactionEntities);

      // Act
      transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);

      // Assert
      verify(readBalanceRepository, never()).findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any());
      verify(transactionRepository, never()).sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any());

      verify(instructionRepository, times(2)).save(instructionEntityCaptor.capture());
      InstructionEntity finalSavedEntity = instructionEntityCaptor.getValue();
      assertEquals(InstructionStatus.PENDING, finalSavedEntity.getStatus());
    }

    @Test
    @DisplayName("Should throw DuplicateTransactionException when a transaction refId already exists")
    void initiateInstruction_shouldThrow_whenTransactionIsDuplicate() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithDebit("-50");
      List<String> existingRefs = List.of(instruction.getTransactions().get(0).getTransactionRefId());

      when(validationService.isProfileAvailable(anyString(), anyString())).thenReturn(true);
      when(validationService.getAccount(anyString(), anyString(), anyString())).thenReturn(activeAccount);
      when(transactionRepository.findExistingTransactionRefs(any(), any(), any())).thenReturn(existingRefs);

      // Act & Assert
      assertThrows(DuplicateTransactionException.class, () ->
          transactionService.initiateInstruction(instruction, profileId, accountId, interactionId));
    }

    @Test
    @DisplayName("Should throw InactiveStatusException when profile is not available")
    void initiateInstruction_shouldThrow_whenProfileIsInactive() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithDebit("-50");
      when(validationService.isProfileAvailable(anyString(), anyString())).thenReturn(false);

      // Act & Assert
      assertThrows(InactiveStatusException.class, () ->
          transactionService.initiateInstruction(instruction, profileId, accountId, interactionId));
    }
  }

  @Nested
  @DisplayName("BalanceCalculation Tests")
  class BalanceCalculationTests {

    @Test
    @DisplayName("Should set status to FAILED when balance is insufficient")
    void initiateInstruction_shouldFail_whenBalanceIsInsufficient() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithDebit("-200");
      InstructionEntity instructionEntity = createInstructionEntity(instruction);
      List<TransactionEntity> transactionEntities = createTransactionEntities(instruction);
      setupCommonMocks(instruction, instructionEntity, transactionEntities);
      activeAccount.getOptions().setOverdraftAmount(new BigDecimal("50"));

      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(false);
      when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any()))
          .thenReturn(Optional.of(createBalanceEntity(new BigDecimal("100"))));
      when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("-200")));
      when(transactionRepository.sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(BigDecimal.ZERO));
      // Effective balance = 100 + 0 + (-200 - 0) = -100.
      // With overdraft: -100 + 50 = -50. Insufficient.

      // Act
      transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);

      // Assert
      verify(instructionRepository, times(2)).save(instructionEntityCaptor.capture());
      InstructionEntity finalSavedEntity = instructionEntityCaptor.getValue();
      assertEquals(InstructionStatus.FAILED, finalSavedEntity.getStatus());
    }

    @Test
    @DisplayName("Should succeed with PENDING when balance is covered by overdraft")
    void initiateInstruction_shouldSucceed_whenBalanceIsCoveredByOverdraft() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithDebit("-120");
      InstructionEntity instructionEntity = createInstructionEntity(instruction);
      List<TransactionEntity> transactionEntities = createTransactionEntities(instruction);
      setupCommonMocks(instruction, instructionEntity, transactionEntities);
      activeAccount.getOptions().setOverdraftAmount(new BigDecimal("50"));

      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(false);
      when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any()))
          .thenReturn(Optional.of(createBalanceEntity(new BigDecimal("100"))));
      when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("-120")));
      when(transactionRepository.sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(BigDecimal.ZERO));
      // Effective balance = 100 + 0 + (-120 - 0) = -20.
      // With overdraft: -20 + 50 = 30. Sufficient.

      // Act
      transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);

      // Assert
      verify(instructionRepository, times(2)).save(instructionEntityCaptor.capture());
      assertEquals(InstructionStatus.PENDING, instructionEntityCaptor.getValue().getStatus());
    }

    @Test
    @DisplayName("Should set FAILED when no snapshot and overdraft is exceeded")
    void initiateInstruction_shouldFail_whenNoSnapshotAndOverdraftExceeded() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithDebit("-600");
      InstructionEntity instructionEntity = createInstructionEntity(instruction);
      List<TransactionEntity> transactionEntities = createTransactionEntities(instruction);
      setupCommonMocks(instruction, instructionEntity, transactionEntities);
      activeAccount.getOptions().setOverdraftAmount(new BigDecimal("500"));

      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(false);
      when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any()))
          .thenReturn(Optional.empty());
      when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("-600")));
      when(transactionRepository.sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(BigDecimal.ZERO));
      // Effective balance = 0 + 0 + (-600 - 0) = -600.
      // With overdraft: -600 + 500 = -100. Insufficient.

      // Act
      transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);

      // Assert
      verify(instructionRepository, times(2)).save(instructionEntityCaptor.capture());
      assertEquals(InstructionStatus.FAILED, instructionEntityCaptor.getValue().getStatus());
    }

    @Test
    @DisplayName("getTotalAmount (JPA) should correctly subtract updated transactions")
    void getTotalAmount_shouldSubtractUpdatedTransactions_usingJpa() throws Exception {
      // Arrange
      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(false);
      when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("500.00")));
      when(transactionRepository.sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("100.00")));

      // Act
      Optional<BigDecimal> result = transactionService.getTotalAmount(accountId, profileId, 123, LocalDateTime.now());

      // Assert
      assertTrue(result.isPresent());
      assertEquals(0, new BigDecimal("400.00").compareTo(result.get()));
    }

    @Test
    @DisplayName("getTotalAmount (JPA) should handle empty optionals gracefully")
    void getTotalAmount_shouldHandleEmptyOptionals_usingJpa() throws Exception {
      // Arrange
      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(false);
      when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
          .thenReturn(Optional.empty());
      when(transactionRepository.sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any()))
          .thenReturn(Optional.empty());

      // Act
      Optional<BigDecimal> result = transactionService.getTotalAmount(accountId, profileId, 123, LocalDateTime.now());

      // Assert
      assertTrue(result.isPresent());
      assertEquals(0, BigDecimal.ZERO.compareTo(result.get()));
    }

    @Test
    @DisplayName("getTotalAmount should call stored procedure when flag is true")
    void getTotalAmount_shouldCallStoredProcedure_whenFlagIsTrue() throws Exception {
      // Arrange
      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(true);
      when(transactionRepository.sumAmountByDateRangeInstructionId_SP(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("123.45")));

      // Act
      Optional<BigDecimal> result = transactionService.getTotalAmount(accountId, profileId, 123, LocalDateTime.now());

      // Assert
      assertTrue(result.isPresent());
      assertEquals(0, new BigDecimal("123.45").compareTo(result.get()));
      verify(transactionRepository).sumAmountByDateRangeInstructionId_SP(any(), any(), any(), any(), any());
      verify(transactionRepository, never()).sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any());
      verify(transactionRepository, never()).sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("Should include pending amount in balance calculation with rollbacks")
    void initiateInstruction_shouldIncludePendingAmount_withRollbacks() throws Exception {
      // Arrange
      Instruction instruction = createInstructionWithDebit("-150");
      InstructionEntity instructionEntity = createInstructionEntity(instruction);
      List<TransactionEntity> transactionEntities = createTransactionEntities(instruction);
      setupCommonMocks(instruction, instructionEntity, transactionEntities);

      when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(false);
      when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any()))
          .thenReturn(Optional.of(createBalanceEntity(new BigDecimal("100"), new BigDecimal("50"))));
      when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("-150")));
      when(transactionRepository.sumPendingToRollbackedTransitionsSince(any(), any(), any(), any(), any()))
          .thenReturn(Optional.of(new BigDecimal("-100")));
      // Effective balance = 100 + 50 + (-150 - (-100)) = 100 + 50 - 50 = 100. Sufficient.

      // Act
      transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);

      // Assert
      verify(instructionRepository, times(2)).save(instructionEntityCaptor.capture());
      assertEquals(InstructionStatus.PENDING, instructionEntityCaptor.getValue().getStatus());
    }
  }

  @Nested
  @DisplayName("commitTransaction Tests")
  class CommitTransactionTests {
    // ... (commit tests are unchanged and should still work) ...
  }

  // --- Helper Methods ---

  private void setupCommonMocks(Instruction instruction, InstructionEntity instructionEntity, List<TransactionEntity> transactionEntities) throws Exception {
    when(validationService.isProfileAvailable(anyString(), anyString())).thenReturn(true);
    when(validationService.getAccount(anyString(), anyString(), anyString())).thenReturn(activeAccount);
    when(transactionRepository.findExistingTransactionRefs(any(), any(), any())).thenReturn(Collections.emptyList());
    when(transactionMapper.fromInstructionToInstructionEntity(any(Instruction.class))).thenReturn(instructionEntity);
    when(transactionMapper.fromTransactionToTransactionEntity(any(Transaction.class))).thenAnswer(invocation -> {
      Transaction t = invocation.getArgument(0);
      return transactionEntities.stream().filter(te -> te.getTransactionRefId().equals(t.getTransactionRefId())).findFirst().orElse(null);
    });
    when(instructionRepository.save(any(InstructionEntity.class))).thenReturn(instructionEntity);
    when(transactionRepository.saveAll(anyList())).thenReturn(transactionEntities);
    when(transactionMapper.fromInstructionEntityToInstruction(any(InstructionEntity.class))).thenReturn(instruction);
  }

  private Account createActiveAccount(String accountId, String profileId) {
    Account account = new Account();
    account.setRefId(accountId);
    account.setProfileId(profileId);
    account.setStatus(AccountStatus.ACTIVE);
    com.peoplestrust.transaction.api.v1.model.Options options = new com.peoplestrust.transaction.api.v1.model.Options();
    options.setFundHoldDays(1);
    options.setOverdraftAmount(BigDecimal.valueOf(500));
    account.setOptions(options);
    return account;
  }

  private Instruction createInstructionWithDebit(String debitAmount) {
    return Instruction.builder()
        .instructionRefId("TEST_INS_" + UUID.randomUUID())
        .accountRefId(accountId)
        .profileRefId(profileId)
        .paymentRail(PaymentRailType.EFT)
        .transactions(List.of(
            createTransaction(TransactionFlowType.DEBIT, debitAmount)
        ))
        .build();
  }

  private Instruction createInstructionWithCreditOnly() {
    return Instruction.builder()
        .instructionRefId("TEST_INS_" + UUID.randomUUID())
        .accountRefId(accountId)
        .profileRefId(profileId)
        .paymentRail(PaymentRailType.EFT)
        .transactions(List.of(
            createTransaction(TransactionFlowType.CREDIT, "100"),
            createTransaction(TransactionFlowType.CREDIT, "200")
        ))
        .build();
  }

  private Transaction createTransaction(TransactionFlowType flow, String amount) {
    return Transaction.builder()
        .transactionRefId(UUID.randomUUID().toString())
        .transactionFlow(flow)
        .paymentCategory(flow == TransactionFlowType.DEBIT ? PaymentCategoryType.CREDIT_PUSH : PaymentCategoryType.DEBIT_PULL)
        .amount(new BigDecimal(amount))
        .monetaryUnit(MonetaryUnit.CAD.name())
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();
  }

  private InstructionEntity createInstructionEntity(Instruction instruction) {
    InstructionEntity entity = new InstructionEntity();
    entity.setId(1);
    entity.setInstructionRefId(instruction.getInstructionRefId());
    entity.setAccountRefId(UUID.fromString(instruction.getAccountRefId()));
    entity.setProfileRefId(UUID.fromString(instruction.getProfileRefId()));
    entity.setPaymentRail(instruction.getPaymentRail());
    List<TransactionEntity> transactionEntities = createTransactionEntities(instruction);
    entity.setTransactions(transactionEntities);
    transactionEntities.forEach(t -> t.setInstruction(entity));
    return entity;
  }

  private List<TransactionEntity> createTransactionEntities(Instruction instruction) {
    if (instruction.getTransactions() == null) {
      return Collections.emptyList();
    }
    return instruction.getTransactions().stream().map(t ->
        TransactionEntity.builder()
            .id((int) (Math.random() * 1000))
            .transactionRefId(t.getTransactionRefId())
            .transactionFlow(t.getTransactionFlow())
            .paymentCategory(t.getPaymentCategory())
            .amount(t.getAmount())
            .acceptanceDateTime(t.getAcceptanceDateTime().toLocalDateTime())
            .build()
    ).collect(Collectors.toList());
  }

  private BalanceEntity createBalanceEntity(BigDecimal totalAmount, BigDecimal totalPendingAmount) {
    BalanceEntity balanceEntity = new BalanceEntity();
    balanceEntity.setTotalAmount(totalAmount);
    balanceEntity.setTotalPendingAmount(totalPendingAmount);
    balanceEntity.setEffectiveToDateTime(LocalDateTime.now().minusDays(1));
    return balanceEntity;
  }

  private BalanceEntity createBalanceEntity(BigDecimal totalAmount) {
    return createBalanceEntity(totalAmount, BigDecimal.ZERO);
  }
}
