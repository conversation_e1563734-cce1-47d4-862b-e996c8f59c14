import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import jsconfigPaths from 'vite-jsconfig-paths'

export default defineConfig({
  plugins: [
    react({
       jsxRuntime: 'automatic',
    }),
    jsconfigPaths
  ],
  resolve: {
    alias: {
      '@': '/src',
    },
  },
  build: {
    rollupOptions: {
		treeshake: true,
		output: {
            manualChunks(id) {
                if (id.includes('node_modules')) {
                    // Get the part after node_modules/
                    let modulePath = id.split('node_modules/')[1];
                    // If pnpm stores the module in a ".pnpm" folder, remove that part.
                    if (modulePath.startsWith('.pnpm/')) {
                        modulePath = modulePath.slice(6);
                    }

                    const segments = modulePath.split('/');
                    let pkgName = segments[0];

                    // If it's a scoped package (which in pnpm appears as "@scope+pkg@version")
                    if (pkgName.startsWith('@') && pkgName.includes('+')) {
                        // Transform "@scope+pkg@version" into "@scope/pkg"
                        const [scope, rest] = pkgName.split('+');
                        if (rest) {
                            const [name] = rest.split('@');
                            pkgName = `${scope}/${name}`;
                        }
                    } else {
                        // For non-scoped packages, remove version info if present.
                        pkgName = pkgName.split('@')[0];
                    }
                    return pkgName;
                }
            },
		},
		onwarn: ({ loc }) => {
            // Optionally filter out warnings from specific dependencies
            if (loc?.file?.match(/js-sha256\/src\/sha256\.js$/)) return;
            // console.warn(message);
		},
	},
  }
})