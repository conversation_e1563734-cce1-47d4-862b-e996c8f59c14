apiVersion: batch/v1
kind: CronJob
metadata:
  name: etransfer-recon-job
  namespace: pg-ledger-qa
  labels:
    app: etransfer-recon-job-v1
    domain: general-ledger
    tags.datadoghq.com/env: qa
    tags.datadoghq.com/service: etransfer-recon-job-v1
    tags.datadoghq.com/version: "**************"
spec:
  schedule: "* * 31 2 *"
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            admission.datadoghq.com/python-lib.version: latest
          labels:
            app: etransfer-recon-job-v1
            admission.datadoghq.com/enabled: "false"
            domain: general-ledger
            tags.datadoghq.com/env: qa
            tags.datadoghq.com/service: etransfer-recon-job-v1
            tags.datadoghq.com/version: "**************"
        spec:
          restartPolicy: OnFailure
          containers:
            - name: etransfer-recon-job-container
              image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/etransfer-recon-job:**************
              args:
                - "--s3-bucket"
                - "pg-gl-recon-qa"
                - "--s3-key"
                - "PLACEHOLDER_KEY"
              resources:
                requests:
                  memory: "384Mi"
                  cpu: "100m"
                limits:
                  memory: "512Mi"
                  cpu: "400m"
              env:
                # --- Original ENV variables ---
                - name: GL_ACCOUNT_ID
                  valueFrom:
                    secretKeyRef:
                      name: etransfer-config
                      key: app.gl-account-id
                - name: ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: gl-recon-aws
                      key: sys.pg.gl.s3.access.key
                - name: SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: gl-recon-aws
                      key: sys.pg.gl.s3.secret.key
                - name: AWS_REGION
                  valueFrom:
                    secretKeyRef:
                      name: gl-recon-aws
                      key: sys.pg.gl.s3.region

                - name: DD_AGENT_HOST
                  valueFrom:
                    fieldRef:
                      fieldPath: status.hostIP
                - name: DD_DOGSTATSD_URL
                  value: "unix:///var/run/datadog/dsd.socket"

                # --- ✅ Added for DB connection ---
                - name: ACCOUNT_DB_HOST
                  valueFrom:
                    secretKeyRef:
                      name: account-database
                      key: sys.database.read.host
                - name: ACCOUNT_DB_PORT
                  value: "5432"
                - name: ACCOUNT_DB_NAME
                  valueFrom:
                    secretKeyRef:
                      name: account-database
                      key: sys.database.name
                - name: ACCOUNT_DB_USERNAME
                  valueFrom:
                    secretKeyRef:
                      name: account-database
                      key: sys.database.username
                - name: ACCOUNT_DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: account-database
                      key: sys.database.password
                - name: ACCOUNT_DB_SCHEMA
                  valueFrom:
                    secretKeyRef:
                      name: account-database
                      key: sys.database.schema

              volumeMounts:
                - name: dogstatsd-socket
                  mountPath: /var/run/datadog

          volumes:
            - name: dogstatsd-socket
              hostPath:
                path: /var/run/datadog
