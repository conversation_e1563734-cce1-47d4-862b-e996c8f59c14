package com.peoplestrust.transaction.persistence.repository.write;

import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TransactionRepository extends JpaRepository<TransactionEntity, Integer> {


  /**
   * (Internal ONLY) Retrieves transaction by its reference ID.
   *
   * @param transactionRefId transaction reference ID
   * @return
   */
  TransactionEntity findByTransactionRefId(String transactionRefId);


  /**
   * Return sum of all CREDITS and DEBITS within a date range and up to the current instruction.
   *
   * @param accountRefId           account reference ID
   * @param profileRefId           profile reference ID
   * @param effectiveDateTimeStart date and time to start (inclusive) searching from
   * @param effectiveDateTimeUntil date and time to search until (inclusive)
   * @param instructionId
   * @return
   */
  @Query(value = "select sum(amount) FROM TransactionEntity where "
      + " (accountRefId=:accountRefId and profileRefId=:profileRefId) "
      + " and status in ('POSTED','PENDING','INIT_PENDING','REVERSED') "
      + " and (effectiveDateTime >=:effectiveDateTimeStart and effectiveDateTime <=:effectiveDateTimeUntil) "
      + " and instruction.id <> :instructionId ")
  Optional<BigDecimal> sumAmountByDateRangeInstructionId(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeUntil") LocalDateTime effectiveDateTimeUntil,
      @Param("instructionId") Integer instructionId);

  @Query(nativeQuery = true, value = "SELECT sumAmountByDateRangeInstructionId(:accountRefId, :profileRefId, :effectiveDateTimeStart, :effectiveDateTimeUntil, :instructionId)")
  Optional<BigDecimal> sumAmountByDateRangeInstructionId_SP(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("effectiveDateTimeStart") LocalDateTime effectiveDateTimeStart,
      @Param("effectiveDateTimeUntil") LocalDateTime effectiveDateTimeUntil,
      @Param("instructionId") Integer instructionId);

  /**
   * ` Retrieve transaction by profile, account, instruction and transaction reference IDs.
   *
   * @param instructionRefId instruction reference ID
   * @param transactionRefId transaction reference ID
   * @param profileRefId     profile reference ID
   * @param accountRefId     account reference ID
   * @return list of transactions
   */
  @Query(value = "FROM TransactionEntity t where "
      + " instruction.instructionRefId =:instructionRefId and transactionRefId =:transactionRefId and "
      + " t.profileRefId =:profileRefId and t.accountRefId =:accountRefId ")
  List<TransactionEntity> findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(
      @Param("instructionRefId") String instructionRefId,
      @Param("transactionRefId") String transactionRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("accountRefId") UUID accountRefId);

  /**
   * Return every transactionRefId that already exists for this profile + account and is in the incoming list.
   */
  @Query("""
      select t.transactionRefId
      from   TransactionEntity t
      where  t.profileRefId   = :profile
        and  t.accountRefId   = :account
        and  t.transactionRefId in :refs
      """)
  List<String> findExistingTransactionRefs(@Param("profile") UUID profile,
      @Param("account") UUID account,
      @Param("refs") List<String> refs);

  /**
   * Find transactions that were PENDING at snapshot time but are now ROLLBACKED
   * These need to be subtracted from the effective balance since they were counted as pending in the snapshot
   */
  /**
   * Find transactions that were PENDING at snapshot time but are now ROLLBACKED.
   * This query is the direct JPA equivalent of the logic in the final Stored Procedure.
   *
   * @param accountRefId          The account identifier.
   * @param profileRefId          The profile identifier.
   * @param snapshotEndTime       The effective_to_date_time of the snapshot we are calculating from.
   * @param lookbackStartTime     The start of the performance window (e.g., snapshotEndTime - 33 days).
   * @param instructionId         The current instruction ID to exclude.
   * @return The sum of amounts for transactions that need to be adjusted.
   */
  @Query("""
    SELECT COALESCE(SUM(t.amount), 0)
    FROM TransactionEntity t
    WHERE t.accountRefId = :accountRefId
      AND t.profileRefId = :profileRefId
      AND t.status IN ('ROLLBACKED', 'ROLLBACKED_SYSTEM')
      AND t.effectiveDateTime < :snapshotEndTime
      AND t.finalizationDateTime > :snapshotEndTime
      AND t.effectiveDateTime > :lookbackStartTime
      AND NOT EXISTS (
          SELECT 1 FROM BalanceEntity b
          WHERE b.accountRefId = :accountRefId
            AND b.profileRefId = :profileRefId
            AND b.effectiveToDateTime = :snapshotEndTime
            AND b.createdDateTime > t.finalizationDateTime
      )
      AND t.instruction.id <> :instructionId
    """)
  Optional<BigDecimal> sumPendingToRollbackedTransitionsSince(
      @Param("accountRefId") UUID accountRefId,
      @Param("profileRefId") UUID profileRefId,
      @Param("snapshotEndTime") LocalDateTime snapshotEndTime,
      @Param("lookbackStartTime") LocalDateTime lookbackStartTime,
      @Param("instructionId") Integer instructionId);
}
