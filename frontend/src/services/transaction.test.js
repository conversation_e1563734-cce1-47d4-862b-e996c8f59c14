import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createUpdateMetadata } from './transaction';
import { customInstance } from './client/axiosInstance';

// Mock the axios instance
vi.mock('./client/axiosInstance');

describe('createUpdateMetadata', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should send metadata in the request body', async () => {
    // Arrange
    const mockResponse = { status: 201 };
    vi.mocked(customInstance).mockResolvedValue(mockResponse);

    const testData = {
      profileId: 'test-profile-id',
      accountId: 'test-account-id',
      instructionRefId: 'test-instruction-id',
      transactionRefId: 'test-transaction-id',
      metadata: {
        ticket_id: 'JIRA-123',
        ticket_type: 'JIRA',
        notes: 'Test metadata notes'
      }
    };

    // Act
    const result = await createUpdateMetadata(testData);

    // Assert
    expect(customInstance).toHaveBeenCalledWith({
      url: 'transaction/test-instruction-id/test-transaction-id/metadata',
      method: 'PUT',
      data: testData.metadata,
      headers: {
        'x-pg-profile-id': 'test-profile-id',
        'x-pg-account-id': 'test-account-id',
        'Content-Type': 'application/json'
      }
    });
    expect(result).toBe(true);
  });

  it('should return true for 204 status', async () => {
    // Arrange
    const mockResponse = { status: 204 };
    vi.mocked(customInstance).mockResolvedValue(mockResponse);

    const testData = {
      profileId: 'test-profile-id',
      accountId: 'test-account-id',
      instructionRefId: 'test-instruction-id',
      transactionRefId: 'test-transaction-id',
      metadata: { notes: 'Test notes' }
    };

    // Act
    const result = await createUpdateMetadata(testData);

    // Assert
    expect(result).toBe(true);
  });

  it('should throw error when request fails', async () => {
    // Arrange
    const mockError = new Error('Network error');
    vi.mocked(customInstance).mockRejectedValue(mockError);

    const testData = {
      profileId: 'test-profile-id',
      accountId: 'test-account-id',
      instructionRefId: 'test-instruction-id',
      transactionRefId: 'test-transaction-id',
      metadata: { notes: 'Test notes' }
    };

    // Act & Assert
    await expect(createUpdateMetadata(testData)).rejects.toThrow('Network error');
  });
});
