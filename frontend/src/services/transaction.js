import { customInstance } from "./client/axiosInstance";
import generateQueryString from "@/utils/generateQueryString";
import parseEtDateToUtc from "@/utils/parseDate";

const generateHeader = (profile, account) => ({
  "x-pg-profile-id": profile,
  "x-pg-account-id": account,
  "Content-Type": "application/json",
});

export const manualAdjustmentTransaction = async ({
  ref_id,
  profileId,
  accountId,
  ...data
}) => {
  const transactions = [1].map(() => ({
    amount: data.amount,
    transaction_flow: data.transaction_flow,
    transaction_ref_id: `x-pg-${ref_id}`,
    payment_category: "CORRECTION",
    monetary_unit: "CAD",
  }));

  const headers = generateHeader(profileId, accountId);
  const response = await customInstance({ 
      url: "transaction/internal", 
      method: 'POST',
      data: {
        instruction_ref_id: `x-pg-${ref_id}`,
        payment_rail: "INTERNAL",
        transactions,
      },
      headers
  });
  
  return response?.data;
};

export const prefundTransaction = async ({
  ref_id,
  profileId,
  accountId,
  ...data
}) => {
  const transactions = [1].map(() => ({
    ...data,
    transaction_ref_id: `x-pg-${ref_id}`,
    payment_category: "PREFUND_RESERVE",
    monetary_unit: "CAD",
  }));

  const headers = generateHeader(profileId, accountId);
  const response = await customInstance({ 
      url: "transaction/internal", 
      method: 'POST',
      data: {
        instruction_ref_id: `x-pg-${ref_id}`,
        payment_rail: "INTERNAL",
        transactions,
      },
      headers
  });

  return response.data;
};

export const getTransactions = async ({
  profileId,
  accountId,
  instructionId,
}) => {
  const headers = generateHeader(profileId, accountId);
  const response = await customInstance({ 
      url: `transaction/${instructionId}`, 
      method: 'GET',
      headers
  });

  return response.data;
};

export const getInstructions = async (data, global) => {
  const { from_date, to_date, ...rest } = data?.search || {};
  const { profile_id, account_id } = global || {};
  const profileIdQuery = profile_id ? `profile-id=${profile_id}&` : "";
  const accountQuery = account_id ? `account-id=${account_id}&` : "";
  const search = generateQueryString(rest);

  const start = parseEtDateToUtc(from_date, "from_date");
  const end = parseEtDateToUtc(to_date, "to_date");

  const fromDateQuery = !!from_date ? `&start_time=${start}` : "";
  const toDateQuery = !!to_date ? `&end_time=${end}` : "";
  const url = `transaction/search/instruction?${profileIdQuery}${accountQuery}${search}${
      search ? "&" : ""
    }offset=${data.limit * data.page}&max_items=${
      data.limit
    }${fromDateQuery}${toDateQuery}`

  const response = await customInstance({ 
      url, 
      method: 'POST'
  });

  return response.data;
};

export const getTransactionById = async ({
  profileId,
  accountId,
  instructionId,
  transactionId,
}) => {
  const headers = generateHeader(profileId, accountId);

  const response = await customInstance({ 
      url:  `transaction/${instructionId}/${transactionId}`, 
      method: 'GET',
      headers
  });

  return response.data;
};

export const searchTransaction = async (data) => {
  const { profile_id, from_date, to_date, ...rest } = data?.search || {};
  if (!profile_id) {
    return { transactions: [] };
  }

  const start = parseEtDateToUtc(from_date, "from_date");
  const end = parseEtDateToUtc(to_date, "to_date");

  const profileIdQuery = profile_id ? `profile-id=${profile_id}&` : "";
  const search = generateQueryString(rest);
  const fromDateQuery = start ? `&start_date=${start}` : "";
  const toDateQuery = end ? `&end_date=${end}` : "";

  const url = `transaction/search?${profileIdQuery}${search}${
    search ? "&" : ""
  }offset=${data.limit * data.page}&max_items=${
    data.limit
  }${fromDateQuery}${toDateQuery}`;

  try {
    const response = await customInstance({ 
      url, 
      method: 'POST',
    });
    return response.data;
  } catch (error) {
    console.error("searchTransaction Error:", error);
    throw error;
  }
};

// Create or Update Metadata
export const createUpdateMetadata = async ({
  profileId,
  accountId,
  instructionRefId,
  transactionRefId,
  metadata,
}) => {
  const headers = generateHeader(profileId, accountId);
  try {
    const response = await customInstance({
      url:  `transaction/${instructionRefId}/${transactionRefId}/metadata`,
      method: 'PUT',
      data: metadata,
      headers
    });
    return response.status === 201 || response.status === 204;
  } catch (error) {
    console.error("Error updating metadata:", error);
    throw error;
  }
};

// Get Metadata
export const getMetadata = async ({
  profileId,
  accountId,
  instructionRefId,
  transactionRefId,
}) => {
  const headers = generateHeader(profileId, accountId);
  try {
    const url = `transaction/${instructionRefId}/${transactionRefId}/metadata`;
    const response = await customInstance({ 
      url, 
      method: 'GET',
      validationStatus: (status) => {
        return (status >= 200 && status < 300) || status === 404; // Accept 404 as valid
      },
      headers
    });

    if (response.status === 404) {
      // Metadata not found, return null, we will not throw exception
      return null;
    } else {
      return response.data;
    }
  } catch (error) {
    if (error.response && error.response.status === 404) {
      // Metadata not found, return null
      return null;
    } else {
      // Handle other errors
      throw error;
    }
  }
};
