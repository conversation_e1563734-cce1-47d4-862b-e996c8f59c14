import json
import logging
import os
import subprocess
import random
from kubernetes import client, config
from kubernetes.client.rest import ApiException

# Configure logging
logging.basicConfig(level=logging.INFO, force=True)
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
  """
  Lambda function to trigger a Kubernetes Job manually when S3 files are uploaded.
  """
  logger.info("=== Starting Job Trigger Lambda ===")
  try:
    # Extract S3 event information
    s3_bucket = None
    s3_key = None
    
    logger.info(f"Received event: {json.dumps(event, indent=2)}")
    
    # Parse S3 event to extract bucket and key
    if 'Records' in event and len(event['Records']) > 0:
      s3_record = event['Records'][0]
      if 's3' in s3_record:
        s3_bucket = s3_record['s3']['bucket']['name']
        s3_key = s3_record['s3']['object']['key']
        logger.info(f"S3 Event - Bucket: {s3_bucket}, Key: {s3_key}")
      else:
        logger.error("No S3 information found in event record")
        raise Exception("Invalid S3 event format - missing s3 data")
    else:
      logger.error("No Records found in event")
      raise Exception("Invalid event format - missing Records")
    
    if not s3_bucket or not s3_key:
      raise Exception(f"Failed to extract S3 information - Bucket: {s3_bucket}, Key: {s3_key}")

    # Get configuration from environment variables
    cluster_name = os.environ.get("CLUSTER_NAME", "gl-eks-main-dev")
    region = os.environ.get("LAMBDA_REGION", "ca-central-1")
    namespace = os.environ.get("NAMESPACE", "pg-ledger-dev")
    job_name = os.environ.get("JOB_NAME", "etransfer-recon-job")
    logger.info(f"Configuration - Cluster: {cluster_name}, Region: {region}, Namespace: {namespace}, Job: {job_name}")

    # Create kubeconfig
    config_file_path = "/tmp/.kube/config"
    kube_dir = os.path.dirname(config_file_path)
    os.makedirs(kube_dir, exist_ok=True)
    os.environ['AWS_DEFAULT_REGION'] = region
    logger.info("Creating kubeconfig...")
    result = subprocess.run([
      "aws", "eks", "update-kubeconfig",
      "--name", cluster_name,
      "--region", region,
      "--kubeconfig", config_file_path
    ], capture_output=True, text=True, timeout=30)
    if result.returncode != 0:
      raise Exception(f"Failed to create kubeconfig: {result.stderr}")
    logger.info("Kubeconfig created successfully")

    # Load Kubernetes configuration
    config.load_kube_config(config_file=config_file_path)

    # Create Kubernetes API clients
    batch_v1 = client.BatchV1Api()
    logger.info(f"Fetching Job '{job_name}' from namespace '{namespace}'...")

    # Get the Job
    try:
      job = batch_v1.read_namespaced_cron_job(name=job_name, namespace=namespace)
      logger.info(f"Found Job: {job.metadata.name}")
    except ApiException as e:
      if e.status == 404:
        raise Exception(f"Job '{job_name}' not found in namespace '{namespace}'")
      else:
        raise Exception(f"Error fetching Job: {e}")

    # Create a Job from the Job template
    job_name = f"{job_name}-{random.randint(1, 1000000000)}"
    logger.info(f"Creating one-off Job '{job_name}' from Job template...")

    # Extract the pod template spec from the Job
    pod_template_spec = job.spec.job_template.spec.template.spec

    # Extract containers with ALL their properties
    containers = []
    for container in pod_template_spec.containers:
      container_dict = {
        "name": container.name,
        "image": container.image,
      }

      if container.command:
        container_dict["command"] = container.command

      # Override container args with dynamic S3 information
      if container.args:
        # Replace hardcoded S3 bucket and key with actual values from the event
        updated_args = []
        i = 0
        while i < len(container.args):
          arg = container.args[i]
          if arg == "--s3-bucket" and i + 1 < len(container.args):
            updated_args.append(arg)
            updated_args.append(s3_bucket)  # Use dynamic bucket from S3 event
            logger.info(f"Overriding --s3-bucket with: {s3_bucket}")
            i += 2  # Skip the next argument as it's the old bucket value
          elif arg == "--s3-key" and i + 1 < len(container.args):
            updated_args.append(arg)
            updated_args.append(s3_key)  # Use dynamic key from S3 event
            logger.info(f"Overriding --s3-key with: {s3_key}")
            i += 2  # Skip the next argument as it's the old key value
          else:
            updated_args.append(arg)
            i += 1
        
        container_dict["args"] = updated_args
        logger.info(f"Updated container args: {updated_args}")
      else:
        # If no args exist, create them with S3 information
        container_dict["args"] = ["--s3-bucket", s3_bucket, "--s3-key", s3_key]
        logger.info(f"Created new container args: {container_dict['args']}")

      # IMPORTANT: Preserve volumeMounts
      if container.volume_mounts:
        volume_mounts = []
        for mount in container.volume_mounts:
          mount_dict = {
            "name": mount.name,
            "mountPath": mount.mount_path
          }
          if mount.read_only is not None:
            mount_dict["readOnly"] = mount.read_only
          if mount.sub_path:
            mount_dict["subPath"] = mount.sub_path
          if mount.mount_propagation:
            mount_dict["mountPropagation"] = mount.mount_propagation
          if mount.sub_path_expr:
            mount_dict["subPathExpr"] = mount.sub_path_expr
          volume_mounts.append(mount_dict)

        container_dict["volumeMounts"] = volume_mounts
        logger.info(f"Preserved {len(volume_mounts)} volume mounts for container {container.name}")
        for mount in volume_mounts:
          logger.info(f"  - Mount: {mount['name']} -> {mount['mountPath']}")

      # IMPORTANT: Properly preserve environment variables including secretKeyRef
      if container.env:
        env_vars = []
        for env in container.env:
          env_var = {"name": env.name}

          if env.value is not None:
            env_var["value"] = env.value
          elif env.value_from:
            value_from = {}
            if env.value_from.secret_key_ref:
              value_from["secretKeyRef"] = {
                "name": env.value_from.secret_key_ref.name,
                "key": env.value_from.secret_key_ref.key
              }
              if env.value_from.secret_key_ref.optional is not None:
                value_from["secretKeyRef"]["optional"] = env.value_from.secret_key_ref.optional
            elif env.value_from.config_map_key_ref:
              value_from["configMapKeyRef"] = {
                "name": env.value_from.config_map_key_ref.name,
                "key": env.value_from.config_map_key_ref.key
              }
              if env.value_from.config_map_key_ref.optional is not None:
                value_from["configMapKeyRef"]["optional"] = env.value_from.config_map_key_ref.optional
            elif env.value_from.field_ref:
              value_from["fieldRef"] = {
                "fieldPath": env.value_from.field_ref.field_path
              }
              if env.value_from.field_ref.api_version:
                value_from["fieldRef"]["apiVersion"] = env.value_from.field_ref.api_version
            elif env.value_from.resource_field_ref:
              value_from["resourceFieldRef"] = {
                "resource": env.value_from.resource_field_ref.resource
              }
              if env.value_from.resource_field_ref.container_name:
                value_from["resourceFieldRef"]["containerName"] = env.value_from.resource_field_ref.container_name
              if env.value_from.resource_field_ref.divisor:
                value_from["resourceFieldRef"]["divisor"] = env.value_from.resource_field_ref.divisor

            if value_from:
              env_var["valueFrom"] = value_from

          env_vars.append(env_var)

        container_dict["env"] = env_vars
        logger.info(f"Preserved {len(env_vars)} environment variables for container {container.name}")

      if container.resources:
        resources_dict = {}
        if container.resources.limits:
          resources_dict["limits"] = container.resources.limits
        if container.resources.requests:
          resources_dict["requests"] = container.resources.requests
        if resources_dict:
          container_dict["resources"] = resources_dict

      containers.append(container_dict)

    # Build the pod spec, preserving important fields from the Job
    pod_spec = {
      "containers": containers,
      "restartPolicy": "Never"  # Required for Jobs (Job had "OnFailure" but Jobs need "Never")
    }

    # IMPORTANT: Preserve volumes from the Job
    if pod_template_spec.volumes:
      volumes = []
      for volume in pod_template_spec.volumes:
        volume_dict = {"name": volume.name}

        # Handle different volume types
        if volume.host_path:
          volume_dict["hostPath"] = {
            "path": volume.host_path.path
          }
          if volume.host_path.type:
            volume_dict["hostPath"]["type"] = volume.host_path.type
        elif volume.empty_dir:
          volume_dict["emptyDir"] = {}
          if volume.empty_dir.medium:
            volume_dict["emptyDir"]["medium"] = volume.empty_dir.medium
          if volume.empty_dir.size_limit:
            volume_dict["emptyDir"]["sizeLimit"] = volume.empty_dir.size_limit
        elif volume.config_map:
          volume_dict["configMap"] = {
            "name": volume.config_map.name
          }
          if volume.config_map.items:
            volume_dict["configMap"]["items"] = [
              {"key": item.key, "path": item.path} for item in volume.config_map.items
            ]
          if volume.config_map.default_mode is not None:
            volume_dict["configMap"]["defaultMode"] = volume.config_map.default_mode
          if volume.config_map.optional is not None:
            volume_dict["configMap"]["optional"] = volume.config_map.optional
        elif volume.secret:
          volume_dict["secret"] = {
            "secretName": volume.secret.secret_name
          }
          if volume.secret.items:
            volume_dict["secret"]["items"] = [
              {"key": item.key, "path": item.path} for item in volume.secret.items
            ]
          if volume.secret.default_mode is not None:
            volume_dict["secret"]["defaultMode"] = volume.secret.default_mode
          if volume.secret.optional is not None:
            volume_dict["secret"]["optional"] = volume.secret.optional
        elif volume.persistent_volume_claim:
          volume_dict["persistentVolumeClaim"] = {
            "claimName": volume.persistent_volume_claim.claim_name
          }
          if volume.persistent_volume_claim.read_only is not None:
            volume_dict["persistentVolumeClaim"]["readOnly"] = volume.persistent_volume_claim.read_only

        volumes.append(volume_dict)

      pod_spec["volumes"] = volumes
      logger.info(f"Preserved {len(volumes)} volumes")
      for vol in volumes:
        logger.info(f"  - Volume: {vol['name']} (type: {list(vol.keys())[1] if len(vol) > 1 else 'unknown'})")

    # Preserve ServiceAccount if it exists in the Job
    if hasattr(pod_template_spec, 'service_account_name') and pod_template_spec.service_account_name:
      pod_spec["serviceAccountName"] = pod_template_spec.service_account_name
      logger.info(f"Using ServiceAccount: {pod_template_spec.service_account_name}")
    elif hasattr(pod_template_spec, 'service_account') and pod_template_spec.service_account:
      pod_spec["serviceAccount"] = pod_template_spec.service_account
      logger.info(f"Using ServiceAccount: {pod_template_spec.service_account}")
    else:
      logger.info("No ServiceAccount specified in Job, will use 'default'")

    # Build a clean job manifest
    job_manifest = {
      "apiVersion": "batch/v1",
      "kind": "Job",
      "metadata": {
        "name": job_name,
        "namespace": namespace,
        "labels": {
          "triggered-by": "lambda",
          "source-cronjob": job_name,
          "app": "etransfer-recon-job-v1",
          "domain": "general-ledger"
        },
        "annotations": {
          "job.kubernetes.io/instantiate": "manual",
          "lambda-request-id": context.aws_request_id
        }
      },
      "spec": {
        "template": {
          "metadata": {
            "labels": {
              "job-name": job_name,
              "app": "etransfer-recon-job-v1",
              "domain": "general-ledger"
            }
          },
          "spec": pod_spec
        },
        "backoffLimit": 0  # Don't retry on failure
      }
    }

    # Create the Job
    try:
      created_job = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
      logger.info(f"Successfully created Job: {created_job.metadata.name}")

      return {
        "statusCode": 200,
        "body": json.dumps({
          "message": f"Successfully triggered Job '{job_name}' as Job '{job_name}' for S3 file s3://{s3_bucket}/{s3_key}",
          "job": job_name,
          "namespace": namespace,
          "cluster": cluster_name,
          "s3_bucket": s3_bucket,
          "s3_key": s3_key
        })
      }
    except ApiException as e:
      logger.error(f"Kubernetes API Error: {e}")
      raise Exception(f"Failed to create Job: {e}")
  except Exception as e:
    logger.error(f"Error: {str(e)}")
    logger.exception("Full exception details:")
    return {
      "statusCode": 500,
      "body": json.dumps({
        "error": str(e),
        "type": type(e).__name__
      })
    }