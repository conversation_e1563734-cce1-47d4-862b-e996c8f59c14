#!/bin/bash

# Generate timestamp-based image tag
IMAGE_TAG="$(date -u +"%Y%m%d%H%M%S")"
LAMBDA_FUNCTION_NAME="tol-lambda-eks-qa"  # Update this if your Lambda function name is different
ECR_REPOSITORY="799455639446.dkr.ecr.ca-central-1.amazonaws.com/tol-lambda-eks-qa"
REGION="ca-central-1"

# Check if we should skip Lambda update (for testing)
SKIP_LAMBDA_UPDATE=${1:-false}

echo "IMAGE_TAG is ${IMAGE_TAG}"
echo "Lambda Function: ${LAMBDA_FUNCTION_NAME}"
echo "ECR Repository: ${ECR_REPOSITORY}"

# Configure AWS SSO and login to ECR
echo "Configuring AWS SSO and logging into ECR..."
aws configure sso --profile GL_NON_PROD
aws ecr get-login-password --region ${REGION} --profile=GL_NON_PROD | docker login --username AWS --password-stdin 799455639446.dkr.ecr.ca-central-1.amazonaws.com

# Build and push Docker image
echo "Building Lambda Docker image..."
# Use the original Dockerfile that works
docker build --platform linux/amd64 -t ${ECR_REPOSITORY}:${IMAGE_TAG} .

echo "Pushing image to ECR..."
docker push ${ECR_REPOSITORY}:${IMAGE_TAG}

# Verify the image was pushed successfully
echo "Verifying image in ECR..."
aws ecr describe-images --repository-name tol-lambda-eks-qa --image-ids imageTag=${IMAGE_TAG} --region ${REGION} --profile GL_NON_PROD

if [ "$SKIP_LAMBDA_UPDATE" = "true" ]; then
    echo "⏭️  Skipping Lambda function update (test mode)"
    echo "Image built and pushed: ${ECR_REPOSITORY}:${IMAGE_TAG}"
else
    # Check if Lambda function exists
    echo "Checking if Lambda function exists..."
    aws lambda get-function --function-name ${LAMBDA_FUNCTION_NAME} --region ${REGION} --profile GL_NON_PROD > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "❌ Lambda function ${LAMBDA_FUNCTION_NAME} not found. Please check the function name."
        echo "Available Lambda functions:"
        aws lambda list-functions --region ${REGION} --profile GL_NON_PROD --query 'Functions[].FunctionName' --output table
        exit 1
    fi

    # Update Lambda function with new image
    echo "Updating Lambda function with new image..."
    aws lambda update-function-code \
        --function-name ${LAMBDA_FUNCTION_NAME} \
        --image-uri ${ECR_REPOSITORY}:${IMAGE_TAG} \
        --region ${REGION} \
        --profile GL_NON_PROD
fi

if [ $? -eq 0 ]; then
    echo "✅ Lambda function ${LAMBDA_FUNCTION_NAME} successfully updated with image tag: ${IMAGE_TAG}"
    echo "New image URI: ${ECR_REPOSITORY}:${IMAGE_TAG}"
else
    echo "❌ Failed to update Lambda function"
    exit 1
fi

# Optional: Clean up local Docker images to save space
echo "Cleaning up local Docker images..."
docker image prune -f

echo "🚀 Deployment completed successfully!"