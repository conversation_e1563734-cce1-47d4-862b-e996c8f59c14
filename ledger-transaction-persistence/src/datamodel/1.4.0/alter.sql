-- Step 1: Drop the old function if it exists
DROP FUNCTION IF EXISTS sumAmountByDateRangeInstructionId(UUI<PERSON>, U<PERSON><PERSON>, TIMESTA<PERSON>, <PERSON>IMESTA<PERSON>, INTEGER);

-- Step 2: Create the new sp
CREATE OR REPLACE FUNCTION sumAmountByDateRangeInstructionId(
    p_account_ref_id UUID,
    p_profile_ref_id UUID,
    p_start_date TIMESTAMP,  -- snapshot's effective_to_time
    p_end_date TIMESTAMP,     -- current time
    p_instruction_id INT
)
    RETURNS NUMERIC AS $$
DECLARE
    new_transactions_total NUMERIC;
    pending_to_rollbacked_total NUMERIC;
    total_change NUMERIC;
BEGIN
    -- 1. Sum of NEW transactions since the snapshot
    SELECT COALESCE(SUM(amount), 0)
    INTO new_transactions_total
    FROM transactions
    WHERE account_ref_id = p_account_ref_id
      AND profile_ref_id = p_profile_ref_id
      AND status IN ('POSTED', 'PENDING', 'REVERSED', 'INIT_PENDING')
      AND effective_date_time >= p_start_date
      AND effective_date_time <= p_end_date
      AND instruction_id <> p_instruction_id;

    -- 2. Find transactions that were PENDING at snapshot time but are now ROLLBACKED
    -- These are transactions that:
    -- - Were created before the snapshot period ended
    -- - Were still PENDING or INIT_PENDING at the snapshot time
    -- - Got rollbacked AFTER the snapshot period
    SELECT COALESCE(SUM(amount), 0)
    INTO pending_to_rollbacked_total
    FROM transactions t
    WHERE t.account_ref_id = p_account_ref_id
      AND t.profile_ref_id = p_profile_ref_id
      AND t.status IN ('ROLLBACKED', 'ROLLBACKED_SYSTEM')
      AND t.effective_date_time < p_start_date  -- Created before snapshot
      AND t.finalization_date_time > p_start_date  -- Rollbacked after snapshot period
      AND t.effective_date_time > (p_start_date - INTERVAL '33 days') --for performance
      AND NOT EXISTS (  -- Make sure it wasn't already rollbacked at snapshot time
        SELECT 1 FROM balance bs
        WHERE bs.account_ref_id = p_account_ref_id
          AND bs.profile_ref_id = p_profile_ref_id
          AND bs.effective_to_date_time = p_start_date
          AND bs.created_date_time > t.finalization_date_time
    )
      AND instruction_id <> p_instruction_id;

    total_change := new_transactions_total - pending_to_rollbacked_total;

    RETURN total_change;
END;
$$ LANGUAGE plpgsql;

-- Composite index for the NEW transactions query
CREATE INDEX CONCURRENTLY idx_transactions_new_composite
    ON transactions (
                     account_ref_id,
                     profile_ref_id,
                     effective_date_time,
                     status,
                     instruction_id
        )
    INCLUDE (amount)
    WHERE status IN ('POSTED', 'PENDING', 'INIT_PENDING', 'REVERSED');

-- Composite index for UPDATED transactions query
CREATE INDEX CONCURRENTLY idx_transactions_rollback_composite
    ON transactions (
                     account_ref_id,
                     profile_ref_id,
                     finalization_date_time,
                     status,
                     effective_date_time,
                     instruction_id
        )
    INCLUDE (amount)
    WHERE status IN ('ROLLBACKED', 'ROLLBACKED_SYSTEM');

-- This index is ESSENTIAL for the performance of the NOT EXISTS subquery.
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_balance_snapshot_lookup
    ON balance (account_ref_id, profile_ref_id, effective_to_date_time, created_date_time DESC);