-- ###############
-- # ENVIRONMENT #
-- ###############
-- # Based on env, set search path accordingly (uncomment one of the below)

-- # LOCAL
-- CREATE SCHEMA IF NOT EXISTS ledger_transaction
-- SET search_path to ledger_transaction;

-- # DEV 
-- SET search_path to transaction_dev;

-- # QAS
--  SET search_path to transaction_qa;

-- #####
-- TYPES
-- #####
DO $$ BEGIN
    -- Creating ENUM for instruction::status
    CREATE TYPE instruction_status_type AS ENUM (
        'INIT_PENDING',
        'PENDING',
        'FAILED',
        'ROLLBACKED',
        'ROLL<PERSON>CKED_SYSTEM',
        'POSTED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    -- Creating ENUM for transaction::Status
    CREATE TYPE transaction_status_type AS ENUM (
        'INIT_PENDING',
        'PENDING',
        'FAILED',
        'ROLLBACKED',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_SYSTEM',
        'POSTED',
        'REVERSED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Creating ENUM for transaction::holdtype
DO $$ BEGIN
    CREATE TYPE transaction_hold_type AS ENUM (
        'HOLD',
        'INSTANT');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;


-- ######
-- TABLES
-- ######
-- Creating INSTRUCTIONS table
CREATE TABLE IF NOT exists instructions (
                                            id                          INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
                                            profile_ref_id              UUID NOT NULL,
                                            account_ref_id              UUID NOT NULL,
                                            instruction_ref_id          VARCHAR(36) UNIQUE NOT NULL,
                                            payment_rail                VARCHAR NOT NULL,
                                            status                      instruction_status_type NOT NULL,
                                            created_date_time           TIMESTAMP NOT NULL,
                                            updated_date_time           TIMESTAMP NULL,

                                            CONSTRAINT uq_instruction_ref_id UNIQUE(profile_ref_id, account_ref_id, instruction_ref_id)
);

-- INDEXES for INSTRUCTIONS :: General retrieval
CREATE INDEX IF NOT exists ix_instructions_profile_account_instruction_ref_id ON instructions USING btree (profile_ref_id, account_ref_id, instruction_ref_id);
CREATE INDEX concurrently IF NOT exists ix_instructions_status_created_date_time ON instructions USING btree (status, created_date_time);
CREATE INDEX CONCURRENTLY idx_instructions_rail_partial_internal ON instructions(payment_rail) WHERE payment_rail = 'INTERNAL';

-- Creating TRANSACTIONS table
CREATE TABLE IF NOT exists transactions (
                                            id                          INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
                                            related_id                  INTEGER NULL,
                                            network_payment_ref_id      VARCHAR NULL,
                                            profile_ref_id              UUID NOT NULL,
                                            account_ref_id              UUID NOT NULL,
                                            instruction_id              INTEGER NOT NULL,
                                            transaction_ref_id          VARCHAR(36) NOT NULL,
                                            transaction_hold            transaction_hold_type NULL,
                                            payment_category            VARCHAR NOT NULL,
                                            transaction_flow            VARCHAR NULL,
                                            status                      transaction_status_type NOT NULL,
                                            amount                      NUMERIC(13, 2) NOT NULL,
                                            monetary_unit               VARCHAR(3) NOT NULL,
                                            acceptance_date_time        TIMESTAMP NOT NULL,
                                            due_date_time               TIMESTAMP NULL,
                                            effective_date_time         TIMESTAMP NOT NULL,
                                            created_date_time           TIMESTAMP NOT NULL,
                                            updated_date_time           TIMESTAMP NULL,
                                            finalization_date_time      TIMESTAMP NULL,
                                            CONSTRAINT uq_transaction_ref_iud UNIQUE(profile_ref_id, account_ref_id, transaction_ref_id),
                                            CONSTRAINT fk_transaction_transaction FOREIGN KEY (related_id) REFERENCES transactions(id),
                                            CONSTRAINT fk_instruction_transaction FOREIGN KEY (instruction_id) REFERENCES instructions(id)
);

-- INDEXES for TRANSACTIONS :: Effective date based search
CREATE INDEX IF NOT exists ix_transaction_effective_date_time ON transactions USING btree (effective_date_time);

-- INDEXES for TRANSACTIONS :: General retrieval
CREATE INDEX IF NOT exists ix_transaction_profile_account_instruction ON transactions USING btree (profile_ref_id, account_ref_id, instruction_id);
CREATE INDEX IF NOT exists ix_transaction_profile_account_instruction_transaction ON transactions USING btree (profile_ref_id, account_ref_id, instruction_id, transaction_ref_id);
CREATE INDEX IF NOT exists ix_transaction_related_id ON transactions USING btree (related_id);

create index concurrently if not exists ix_transactions_instruction_id on transactions USING btree(instruction_id);

--Creating BALANCE table
CREATE TABLE IF NOT exists balance (
                                       id                          INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
                                       profile_ref_id              UUID NOT NULL,
                                       account_ref_id              UUID NOT NULL,
                                       total_amount_credit         NUMERIC(13, 2) NOT NULL,
                                       total_amount_debit          NUMERIC(13, 2) NOT NULL,
                                       total_amount                NUMERIC(13, 2) NOT NULL,
                                       total_reserve_amount        NUMERIC(13, 2) NULL,
                                       total_pending_amount        NUMERIC(13, 2) NULL,
                                       monetary_unit               VARCHAR(3) NOT NULL,
                                       effective_from_date_time    TIMESTAMP NOT NULL,
                                       effective_to_date_time      TIMESTAMP NOT NULL,
                                       created_date_time           TIMESTAMP NOT NULL
);

-- INDEXES for BALANCE :: General retrieval
CREATE INDEX concurrently IF NOT exists ix_balance_profile_account ON balance USING btree (profile_ref_id, account_ref_id);

-- Step 2: Create the new sp
CREATE OR REPLACE FUNCTION sumAmountByDateRangeInstructionId(
    p_account_ref_id UUID,
    p_profile_ref_id UUID,
    p_start_date TIMESTAMP,  -- snapshot's effective_to_time
    p_end_date TIMESTAMP,     -- current time
    p_instruction_id INT
)
    RETURNS NUMERIC AS $$
DECLARE
    new_transactions_total NUMERIC;
    pending_to_rollbacked_total NUMERIC;
    total_change NUMERIC;
BEGIN
    -- 1. Sum of NEW transactions since the snapshot
    SELECT COALESCE(SUM(amount), 0)
    INTO new_transactions_total
    FROM transactions
    WHERE account_ref_id = p_account_ref_id
      AND profile_ref_id = p_profile_ref_id
      AND status IN ('POSTED', 'PENDING', 'REVERSED', 'INIT_PENDING')
      AND effective_date_time >= p_start_date
      AND effective_date_time <= p_end_date
      AND instruction_id <> p_instruction_id;

    -- 2. Find transactions that were PENDING at snapshot time but are now ROLLBACKED
    -- These are transactions that:
    -- - Were created before the snapshot period ended
    -- - Were still PENDING or INIT_PENDING at the snapshot time
    -- - Got rollbacked AFTER the snapshot period
    SELECT COALESCE(SUM(amount), 0)
    INTO pending_to_rollbacked_total
    FROM transactions t
    WHERE t.account_ref_id = p_account_ref_id
      AND t.profile_ref_id = p_profile_ref_id
      AND t.status IN ('ROLLBACKED', 'ROLLBACKED_SYSTEM')
      AND t.effective_date_time < p_start_date  -- Created before snapshot
      AND t.finalization_date_time > p_start_date  -- Rollbacked after snapshot period
      AND t.effective_date_time > (p_start_date - INTERVAL '33 days') --for performance
      AND NOT EXISTS (  -- Make sure it wasn't already rollbacked at snapshot time
        SELECT 1 FROM balance bs
        WHERE bs.account_ref_id = p_account_ref_id
          AND bs.profile_ref_id = p_profile_ref_id
          AND bs.effective_to_date_time = p_start_date
          AND bs.created_date_time > t.finalization_date_time
    )
      AND instruction_id <> p_instruction_id;

    total_change := new_transactions_total - pending_to_rollbacked_total;

    RETURN total_change;
END;
$$ LANGUAGE plpgsql;

-- create extra index
CREATE INDEX idx_transactions_status ON transactions(status)
    WHERE status IN ('POSTED','PENDING','INIT_PENDING','REVERSED');

CREATE table IF NOT EXISTS transactions_metadata (
                                                     id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
                                                     transaction_id                  INTEGER NOT NULL REFERENCES transactions(id), -- FK to transactions.ID
                                                     meta_data_json                  JSONB,
                                                     created_date_time               TIMESTAMP NOT NULL,
                                                     updated_date_time               TIMESTAMP NULL
);

CREATE INDEX concurrently idx_transactions_acceptance_date_time ON transactions( acceptance_date_time DESC);

ALTER USER transaction_user SET search_path TO transaction, public;

-- Composite index for the NEW transactions query
CREATE INDEX CONCURRENTLY idx_transactions_new_composite
    ON transactions (
                     account_ref_id,
                     profile_ref_id,
                     effective_date_time,
                     status,
                     instruction_id
        )
    INCLUDE (amount)
    WHERE status IN ('POSTED', 'PENDING', 'INIT_PENDING', 'REVERSED');

-- Composite index for UPDATED transactions query
CREATE INDEX CONCURRENTLY idx_transactions_rollback_composite
    ON transactions (
                     account_ref_id,
                     profile_ref_id,
                     finalization_date_time,
                     status,
                     effective_date_time,
                     instruction_id
        )
    INCLUDE (amount)
    WHERE status IN ('ROLLBACKED', 'ROLLBACKED_SYSTEM');

-- This index is ESSENTIAL for the performance of the NOT EXISTS subquery.
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_balance_snapshot_lookup
    ON balance (account_ref_id, profile_ref_id, effective_to_date_time, created_date_time DESC);